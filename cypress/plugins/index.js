// ***********************************************************
// This example plugins/index.js can be used to load plugins
//
// You can change the location of this file or turn off loading
// the plugins file with the 'pluginsFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/plugins-guide
// ***********************************************************

// This function is called when a project is opened or re-opened (e.g. due to
// the project's config changing)
// const esmRequire = require('esm')(module);
// const createConfig = esmRequire('../../scripts/webpack/lib/createConfig').default;
const webpack = require('webpack');
const webpackPreprocessor = require('@cypress/webpack-preprocessor');
const defaults = webpackPreprocessor.defaultOptions;
const path = require('path');
const { addMatchImageSnapshotPlugin } = require('@simonsmith/cypress-image-snapshot/plugin');

module.exports = (on) => {
  // Allow importing from our code base in e2e browser tests.
  defaults.webpackOptions.resolve = {
    modules: ['node_modules', path.resolve(__dirname, '../../src')],
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
  };

  const transpiledModules = [
    '@qga/roo-ui',
    '@qga/components',
    '@qantasexperiences/analytics',
    '@qantasexperiences/utils',
    '@qantasexperiences/tenants',
  ];

  const transpileRegex = new RegExp(`node_modules\\/(?:${transpiledModules.join('|')})`);

  defaults.webpackOptions.module.rules.push(
    {
      test: /\.(js|jsx|ts|tsx)$/,
      include: [path.resolve(__dirname, '../../src'), (filepath) => transpileRegex.test(filepath)],
      use: {
        loader: 'babel-loader',
        options: {
          presets: ['@babel/preset-env', '@babel/preset-react', '@babel/preset-typescript'],
        },
      },
    },
    {
      test: /\.(woff2?|ttf|svg|png|jpe?g|gif)$/i,
      loader: 'file-loader',
      options: { emitFile: false },
    },
  );

  // Polyfill process.env in the e2e browser tests.
  defaults.webpackOptions.plugins = [
    new webpack.DefinePlugin({
      'process.env': JSON.stringify(process.env),
    }),
  ];

  // Patch webpack 5 to work with Cypress.
  const publicPath = ' ';
  let outputOptions;
  Object.defineProperty(defaults.webpackOptions, 'output', {
    get: () => {
      return { ...outputOptions, publicPath };
    },
    set: function (x) {
      outputOptions = x;
    },
  });
  on('file:preprocessor', webpackPreprocessor(defaults));
  on('before:browser:launch', (browser = {}, launchOptions) => {
    if (browser.name === 'chrome' || browser.name === 'edge') {
      launchOptions.args.push('--disable-features=SameSiteByDefaultCookies'); // bypass 401 unauthorised access on chromium-based browsers
      launchOptions.args.push('--disable-gpu');
      launchOptions.args.push('--force-device-scale-factor=1');
      return launchOptions;
    }
  });

  addMatchImageSnapshotPlugin(on);
};
