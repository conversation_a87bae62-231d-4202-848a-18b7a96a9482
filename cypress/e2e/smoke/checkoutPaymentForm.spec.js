import { submitPaymentOptionsForm, submitPersonalDetailsForm, submitQantasPointsForm } from '../../support/common';
import { COOKIE_NAMES } from 'lib/enums/cookies';

describe('Adyen Drop-in', () => {
  before(() => {
    cy.setCookie(COOKIE_NAMES.IFRAME_CONTENT_LOADED, 'true');
    cy.setCookie(COOKIE_NAMES.QH_USER_GROUP, 'adyen-drop-in-enabled');
    cy.visitCheckoutWithStayAttributes();
  });

  it('renders adyen iframe, has expected class-names for custom styling and matches existing snapshot', () => {
    submitPersonalDetailsForm();
    submitQantasPointsForm(true);
    submitPaymentOptionsForm();

    cy.get('[data-testid="adyen-drop-in"] iframe', { timeout: 10000 }).should('exist');

    cy.contains('Enter your credit or debit card details').should('be.visible');
    cy.contains(/You'll be charged \$\d+\.\d{2} AUD/);
    cy.contains('MM/YY format').should('exist');
    cy.contains(/3 digits.*CVC\/CVV2/).should('exist');

    cy.get('iframe[title="Iframe for card number"]').should('exist');
    cy.get('iframe[title="Iframe for expiry date"]').should('exist');
    cy.get('iframe[title="Iframe for security code"]').should('exist');
    cy.get('input[name="holderName"]').should('exist').and('be.visible');

    cy.get('.adyen-checkout__dropin').within(() => {
      const selectors = [
        '.adyen-checkout__payment-method',
        '.adyen-checkout__payment-method__image__wrapper',
        '.adyen-checkout__card__brands__brand-wrapper',
        '.adyen-checkout-card-input__icon',
        '.adyen-checkout__input',
        '.adyen-checkout__payment-method__name--selected',
        '.adyen-checkout-form-instruction',
        '.adyen-checkout-contextual-text',
        '.adyen-checkout__label__text',
        '.adyen-checkout-contextual-text--error',
      ];

      selectors.forEach((selector) => {
        cy.get(selector).should('exist');
      });
    });

    cy.get('[data-testid="adyen-drop-in"]').matchImageSnapshot('adyen-payment-form');
  });
});
