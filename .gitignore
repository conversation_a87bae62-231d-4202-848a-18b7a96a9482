# See https://help.github.com/ignore-files/ for more about ignoring files.

# Dependencies
/node_modules
.yarn
.yalc
yalc.lock

# Testing
/coverage
/cypress/screenshots
/cypress/videos
/cypress/downloads
/cypress/snapshots/__all
/cypress/snapshots/smoke/**/__diff_output__
/hooroo-ci/bin/BrowserStackLocal-*

# Build outputs
/build
/builds
.next/

# Logs and debugging
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dump.rbd

# Environment and local config
.env.local
.env.*.local
.sentryclirc

# IDE and OS
.vscode
.DS_Store

# Infrastructure
**/.terraform
**/.terraform/modules
**/.terraform/environment
**/terraform.tfstate.*.backup
**/terraform.tfstate.backup
