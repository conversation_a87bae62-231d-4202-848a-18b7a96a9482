import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import ImageLoadingIndicator from './ImageLoadingIndicator';
import theme from 'lib/theme';
import { ThemeProvider } from 'emotion-theming';

const alt = 'Some test image';
const src = 'some/image.jpg';
const srcSet = 'some/image.jpg 1x, some/image-x2.jpg x2';
const defaultProps = { src, srcSet, alt };

jest.mock('@qga/roo-ui/components', () => ({
  Box: ({ children, 'data-testid': dataTestId, ...props }) => (
    <div data-testid={dataTestId} {...props}>
      {children}
    </div>
  ),
  LoadingIndicator: ({ children, ...props }) => (
    <div role="progressbar" {...props}>
      {children}
    </div>
  ),
}));

const renderComponent = (props = {}) => {
  render(
    <ThemeProvider theme={theme}>
      <ImageLoadingIndicator {...defaultProps} {...props} />
    </ThemeProvider>,
  );
};
describe('<ImageLoadingIndicator />', () => {
  it('renders a <LoadingIndicator> when there is no set height or width', async () => {
    renderComponent({ width: undefined, height: undefined });
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('renders a loading indicator when the image is loading and is set to "cover"', async () => {
    renderComponent({ cover: true });
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('renders a shimmer with the correct dimensions when a height and width are provided', async () => {
    renderComponent({ width: '333px', height: '333px' });
    expect(screen.getByTestId('shimmer')).toBeInTheDocument();
    expect(screen.getByTestId('shimmer')).toHaveAttribute('width', '333px');
    expect(screen.getByTestId('shimmer')).toHaveAttribute('height', '333px');
  });

  it('renders a shimmer with 100% height when only width is provided', async () => {
    renderComponent({ width: '333px' });
    expect(screen.getByTestId('shimmer')).toBeInTheDocument();
    expect(screen.getByTestId('shimmer')).toHaveAttribute('width', '333px');
    expect(screen.getByTestId('shimmer')).toHaveAttribute('height', '100%');
  });

  it('renders a shimmer with 100% width when only height is provided', async () => {
    renderComponent({ height: '333px' });
    expect(screen.getByTestId('shimmer')).toBeInTheDocument();
    expect(screen.getByTestId('shimmer')).toHaveAttribute('height', '333px');
    expect(screen.getByTestId('shimmer')).toHaveAttribute('width', '100%');
  });
});
