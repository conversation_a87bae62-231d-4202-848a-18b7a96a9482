// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`ImageInner applies "contain" object-fit when cover is false: ImageInner with cover=false 1`] = `
<div
  class="css-1nd8z3a-Box-Wrapper euojt4k0"
>
  <img
    alt="Test image alt text"
    class="css-na93i2-Inner euojt4k1"
    height="150"
    src="test.jpg"
    width="200"
  />
</div>
`;

exports[`ImageInner applies "cover" object-fit when cover is true and sanityImage is false (default): ImageInner with cover=true and sanityImage=false 1`] = `
<div
  class="css-1nd8z3a-Box-Wrapper euojt4k0"
>
  <img
    alt="Test image alt text"
    class="css-1bd8zow-Inner euojt4k1"
    height="150"
    src="test.jpg"
    width="200"
  />
</div>
`;

exports[`ImageInner applies "fill" object-fit when cover is true and sanityImage is true: ImageInner with cover=true and sanityImage=true 1`] = `
<div
  class="css-1nd8z3a-Box-Wrapper euojt4k0"
>
  <img
    alt="Test image alt text"
    class="css-opeyiw-Inner euojt4k1"
    height="150"
    src="test.jpg"
    width="200"
  />
</div>
`;

exports[`ImageInner applies animation when animate prop is true: ImageInner with animate=true 1`] = `
<div
  class="css-1nd8z3a-Box-Wrapper euojt4k0"
>
  <img
    alt="Test image alt text"
    class="css-1bd8zow-Inner euojt4k1"
    data-animate="true"
    height="150"
    src="test.jpg"
    width="200"
  />
</div>
`;

exports[`ImageInner does not apply animation when animate prop is false: ImageInner with animate=false 1`] = `
<div
  class="css-1nd8z3a-Box-Wrapper euojt4k0"
>
  <img
    alt="Test image alt text"
    class="css-1bd8zow-Inner euojt4k1"
    height="150"
    src="test.jpg"
    width="200"
  />
</div>
`;

exports[`ImageInner passes srcSet prop to the image: ImageInner with srcSet 1`] = `
<div
  class="css-1nd8z3a-Box-Wrapper euojt4k0"
>
  <img
    alt="Test image alt text"
    class="css-1bd8zow-Inner euojt4k1"
    height="150"
    src="test.jpg"
    srcset="test-src-set.jpg 1x, test-src-set-2x.jpg 2x"
    width="200"
  />
</div>
`;

exports[`ImageInner passes through additional props to the Wrapper (Box component): ImageInner with additional props 1`] = `
<div
  class="css-kku2g6-Box-Wrapper euojt4k0"
>
  <img
    alt="Test image alt text"
    class="css-1bd8zow-Inner euojt4k1"
    height="150"
    src="test.jpg"
    width="200"
  />
</div>
`;

exports[`ImageInner renders with correct width and height applied to the Wrapper: ImageInner with custom dimensions 1`] = `
<div
  class="css-1nd8z3a-Box-Wrapper euojt4k0"
>
  <img
    alt="Test image alt text"
    class="css-1bd8zow-Inner euojt4k1"
    height="200px"
    src="test.jpg"
    width="300px"
  />
</div>
`;

exports[`ImageInner renders with required props and default behaviors: ImageInner with default props 1`] = `
<div
  class="css-1nd8z3a-Box-Wrapper euojt4k0"
>
  <img
    alt="Test image alt text"
    class="css-1bd8zow-Inner euojt4k1"
    height="150"
    src="test.jpg"
    width="200"
  />
</div>
`;
