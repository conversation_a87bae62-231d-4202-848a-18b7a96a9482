import React from 'react';
import PropTypes from 'prop-types';
import { Box } from '@qga/roo-ui/components';
import ImageFallback from 'components/Image/ImageFallback';
import ImageLoading from 'components/Image/ImageLoadingIndicator';
import ImageInner from 'components/Image/ImageInner';
import { useImageLoader } from './useImageLoader';

const ImageLoader = ({ src, srcSet, alt, onLoad, width, height, cover, sanityImage, ...rest }) => {
  const { isLoading, displayedLoading, error, loadedImg } = useImageLoader({ src, srcSet, onLoad });
  const isImageReady = !!loadedImg.src || !!loadedImg.srcSet;
  const hasSuppliedDimensions = !!width || !!height;

  const showPlaceholder = !error && !isLoading && !isImageReady;

  const hiddenImage = (src || srcSet) && (
    <img
      src={src}
      srcSet={srcSet}
      alt=""
      data-testid="hidden-image-preload"
      style={{ position: 'absolute', left: '-9999px' }}
      aria-hidden="true"
    />
  );

  if (showPlaceholder)
    return (
      <>
        {hiddenImage}
        <Box width={width} height={height} data-testid="placeholder" />
      </>
    );

  if (isLoading)
    return (
      <>
        {hiddenImage}
        <ImageLoading {...rest} width={width} height={height} role="status" />
      </>
    );

  if (error)
    return (
      <>
        {hiddenImage}
        <ImageFallback {...rest} alt={alt} width={width} height={height} role="alert" />
      </>
    );

  if (isImageReady) {
    const finalWidth = hasSuppliedDimensions ? width || '100%' : loadedImg.naturalWidth;
    const finalHeight = hasSuppliedDimensions ? height || '100%' : loadedImg.naturalHeight;

    return (
      <>
        {hiddenImage}
        <Box {...rest} width={finalWidth} height={finalHeight} data-testid="box">
          <ImageInner
            alt={alt}
            src={loadedImg.src}
            srcSet={loadedImg.srcSet}
            width={finalWidth}
            height={finalHeight}
            animate={displayedLoading}
            cover={cover}
            sanityImage={sanityImage}
          />
        </Box>
      </>
    );
  }

  return hiddenImage;
};

ImageLoader.propTypes = {
  alt: PropTypes.string.isRequired,
  src: PropTypes.string,
  srcSet: PropTypes.string,
  onLoad: PropTypes.func,
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.array]),
  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.array]),
  cover: PropTypes.bool,
  sanityImage: PropTypes.bool,
};

ImageLoader.defaultProps = {
  src: null,
  onLoad: null,
  srcSet: null,
  width: null,
  height: null,
  cover: true,
  sanityImage: false,
};

export default ImageLoader;
