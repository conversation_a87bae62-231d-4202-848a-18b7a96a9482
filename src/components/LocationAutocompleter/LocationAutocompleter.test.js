import React from 'react';
import LocationAutocompleter from './LocationAutocompleter';
import { useLocationData } from './useLocationData';
import { getPageName } from 'store/router/routerSelectors';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { getSearchLocalStorage } from 'components/HomePage/localStorage';
import { render, screen, fireEvent, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import mockStore from 'redux-mock-store';
import { Provider } from 'react-redux';
import { ThemeProvider } from 'emotion-theming';
import { theme } from '@qga/roo-ui';

jest.mock('lib/sanity');

jest.mock('./useLocationData');
jest.mock('store/ui/uiSelectors');
jest.mock('store/router/routerSelectors');
jest.mock('hooks/useBreakpoints');
jest.mock('components/HomePage/localStorage');

jest.mock('components/TextHighlighter', () => (props) => (
  <div data-testid="text-highlighter" data-text={props.text} data-highlight={props.highlightText ?? ''}>
    {props.text}
  </div>
));

const initialLocationName = 'Sydney, NSW, Australia';
const searchText = 'mel';
const updateQuery = jest.fn();
const routeToProperty = jest.fn();
const mockedSavedPayload = [
  {
    adults: 2,
    checkIn: '2024-04-01',
    checkOut: '2024-04-04',
    id: null,
    isPropertySearch: false,
    location: 'Rome, Italy',
    payWith: 'cash',
  },
  {
    adults: 2,
    checkIn: '2025-04-01',
    checkOut: '2025-04-04',
    id: null,
    isPropertySearch: false,
    location: 'Melbourne, Australia',
    payWith: 'cash',
  },
];

const baseProps = {
  locationName: initialLocationName,
  updateQuery: updateQuery,
  routeToProperty: routeToProperty,
};

// Suppress console errors related to missing icons
beforeAll(() => {
  jest.spyOn(console, 'error').mockImplementation((msg, ...args) => {
    if (typeof msg === 'string' && msg.startsWith('Icon')) return;
    // eslint-disable-next-line no-undef
    const _global = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : global;
    return _global.originalConsoleError ? _global.originalConsoleError(msg, ...args) : undefined;
  });
});
afterAll(() => {
  console.error.mockRestore();
});

beforeEach(() => {
  jest.clearAllMocks();
  useLocationData.mockReturnValue([]);
  useBreakpoints.mockReturnValue({ isLessThanBreakpoint: () => false });
});

const configureMockStore = mockStore([]);
const store = configureMockStore({});

const renderWithProviders = (props = {}) => {
  const allProps = { ...baseProps, ...props };
  return render(
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <LocationAutocompleter {...allProps} />
      </ThemeProvider>
    </Provider>,
  );
};

describe('initial rendering', () => {
  it('renders the location name', () => {
    renderWithProviders();
    const searchInput = screen.getByTestId('location-search-input');
    // expect(searchInput).toHaveProp({ placeholder: initialLocationName, value: '' });
    expect(searchInput).toHaveAttribute('placeholder', initialLocationName);
    expect(searchInput).toHaveValue('');
  });

  it('sets the title on the ResponsiveModal', () => {
    renderWithProviders();
    expect(screen.getByText('Find destination or hotel')).toBeInTheDocument();
  });

  it('does not render the Footer', () => {
    renderWithProviders();
    expect(screen.queryByText('Footer')).not.toBeInTheDocument();
  });
});

describe('location search', () => {
  describe('entering the search input', () => {
    it('clears the input field on enter', () => {
      renderWithProviders();
      const input = screen.getByTestId('location-search-input');
      expect(input).toHaveAttribute('placeholder', initialLocationName);
      expect(input).toHaveValue('');
      fireEvent.focus(input);
      expect(input).toHaveAttribute('placeholder', 'Destination, city, hotel name');
      expect(input).toHaveValue('');
    });

    it('opens the responsive modal for mobile devices', () => {
      renderWithProviders();
      const input = screen.getByTestId('location-search-input');
      fireEvent.focus(input);
      expect(screen.getByTestId('close-modal')).toBeInTheDocument();
    });
  });

  describe('with a successful search', () => {
    const locationResult = { type: 'location', fullName: 'Melbourne, VIC, Australia' };
    const propertyResult = { type: 'property', fullName: 'Hilton Melbourne, Melbourne, Australia', id: 1 };

    beforeEach(async () => {
      useLocationData.mockReturnValue([locationResult, propertyResult]);
    });

    it('displays the results', () => {
      renderWithProviders();
      const input = screen.getByTestId('location-search-input');
      fireEvent.focus(input);
      fireEvent.change(input, { target: { value: searchText } });
      const resultItems = screen.getAllByTestId('location-search-result');
      expect(within(resultItems[0]).getByTestId('text-highlighter')).toHaveAttribute('data-text', locationResult.fullName);
      expect(within(resultItems[1]).getByTestId('text-highlighter')).toHaveAttribute('data-text', propertyResult.fullName);
    });

    it('highlights the first item in the results', () => {
      renderWithProviders();
      const input = screen.getByTestId('location-search-input');
      fireEvent.focus(input);
      fireEvent.change(input, { target: { value: searchText } });
      const resultItems = screen.getAllByTestId('location-search-result');
      expect(resultItems[0]).toHaveStyle('background-color: white');
      expect(resultItems[1]).toHaveStyle(`background-color: ${theme.colors.greys.porcelain}`);
    });

    it('highlights the search text in the result item text', async () => {
      renderWithProviders();
      const input = screen.getByTestId('location-search-input');
      await userEvent.click(input);
      await userEvent.type(input, searchText);
      const highlighters = await screen.findAllByTestId('text-highlighter');
      expect(highlighters[0]).toHaveAttribute('data-text', locationResult.fullName);
      expect(highlighters[0]).toHaveAttribute('data-highlight', searchText);
      expect(highlighters[1]).toHaveAttribute('data-text', propertyResult.fullName);
      expect(highlighters[1]).toHaveAttribute('data-highlight', searchText);
    });

    describe('selecting a location', () => {
      it('calls updateQuery with new location', async () => {
        renderWithProviders();
        const input = screen.getByTestId('location-search-input');
        await userEvent.click(input);
        await userEvent.type(input, searchText);
        const resultItems = await screen.findAllByTestId('location-search-result');
        await userEvent.click(resultItems[0]);
        expect(updateQuery).toHaveBeenCalledWith({ location: locationResult.fullName, subRegions: undefined });
      });

      it('sets the search input field value to the selected item name', async () => {
        renderWithProviders();
        const input = screen.getByTestId('location-search-input');
        await userEvent.click(input);
        await userEvent.type(input, searchText);
        const resultItems = await screen.findAllByTestId('location-search-result');
        await userEvent.click(resultItems[0]);
        expect(input).toHaveValue(locationResult.fullName);
      });
    });

    describe('When focusing the input with existing value', () => {
      it('opens the responsive modal', () => {
        renderWithProviders();
        const input = screen.getByTestId('location-search-input');
        fireEvent.focus(input);
        fireEvent.change(input, { target: { value: searchText } });
        const resultItems = screen.getAllByTestId('location-search-result');
        userEvent.click(resultItems[0]);
        fireEvent.focus(input);
        expect(screen.getByText('Find destination or hotel')).toBeInTheDocument();
      });
    });

    describe('When the input is blurred', () => {
      it('resets the input value', async () => {
        renderWithProviders();
        const input = screen.getByTestId('location-search-input');
        await userEvent.click(input);
        await userEvent.type(input, 'Devonport');
        expect(input).toHaveValue('Devonport');
        fireEvent.blur(input);
        expect(input).toHaveAttribute('placeholder', initialLocationName);
        expect(input).toHaveValue('');
      });
    });

    describe('selecting a property', () => {
      it('routes to the property', async () => {
        renderWithProviders();
        const input = screen.getByTestId('location-search-input');
        await userEvent.click(input);
        await userEvent.type(input, searchText);
        const resultItems = await screen.findAllByTestId('location-search-result');
        await userEvent.click(resultItems[1]);
        expect(routeToProperty).toHaveBeenCalledWith({
          id: 1,
          propertyName: 'Hilton Melbourne, Melbourne, Australia',
          excludeParams: ['location', 'propertyName'],
        });
      });
    });
  });

  describe('with an unsuccessful search', () => {
    it('does not display any results', async () => {
      useLocationData.mockReturnValue([]);
      renderWithProviders();
      expect(screen.queryAllByTestId('location-search-result')).toHaveLength(0);
    });
  });
});

describe('Recent Searches', () => {
  beforeEach(() => {
    getPageName.mockReturnValue('home-page');
  });

  describe('and a recent searches list is NOT available', () => {
    beforeEach(() => {
      getSearchLocalStorage.mockReturnValue([]);
    });

    it('does NOT render the recent searches modal on focus', () => {
      renderWithProviders();
      const input = screen.getByTestId('location-search-input');
      fireEvent.focus(input);
      expect(screen.queryByTestId('recent-searches-modal')).not.toBeInTheDocument();
    });
  });

  describe('and a recent searches list is available', () => {
    beforeEach(() => {
      getSearchLocalStorage.mockReturnValue(mockedSavedPayload);
    });

    it('renders the recent searches modal on focus and not the location one', async () => {
      renderWithProviders();
      const input = screen.getByTestId('location-search-input');
      await userEvent.click(input);
      expect(screen.getByTestId('recent-searches-modal')).toBeInTheDocument();
    });

    it('when the clear button is clicked, the recent Search disappears', async () => {
      renderWithProviders();
      const input = screen.getByTestId('location-search-input');
      await userEvent.click(input);
      expect(screen.getByTestId('recent-searches-modal')).toBeInTheDocument();
      const clearBtn = screen.getByTestId('clear-rc-btn');
      await userEvent.click(clearBtn);
      expect(screen.queryByTestId('recent-searches-modal')).not.toBeInTheDocument();
    });

    describe('and the AutocompleteInput is showing', () => {
      const locationResult = { type: 'location', fullName: 'Melbourne, VIC, Australia' };
      const propertyResult = { type: 'property', fullName: 'Hilton Melbourne, Melbourne, Australia', id: 1 };

      it('the Recent Searches disappears', async () => {
        renderWithProviders();
        const input = screen.getByTestId('location-search-input');
        await userEvent.click(input);
        expect(screen.getByTestId('recent-searches-modal')).toBeInTheDocument();

        useLocationData.mockReturnValue([locationResult, propertyResult]);
        fireEvent.change(input, { target: { value: searchText } });
        expect(screen.queryByTestId('recent-searches-modal')).not.toBeInTheDocument();
      });
    });
  });
});

describe('ResultItem rendering', () => {
  const locationItem = { type: 'location', fullName: 'Melbourne, VIC, Australia' };
  const propertyItem = { type: 'property', fullName: 'Hilton Melbourne, Melbourne, Australia', hasLuxOffer: true };
  const inputValue = 'New';

  beforeEach(() => {
    useLocationData.mockReturnValue([locationItem, propertyItem]);
  });

  it('renders correct icon for location and property items', () => {
    renderWithProviders();
    const input = screen.getByTestId('location-search-input');
    fireEvent.focus(input);
    fireEvent.change(input, { target: { value: inputValue } });
    const resultItems = screen.getAllByTestId('location-search-result');
    expect(within(resultItems[0]).getByTestId('text-highlighter')).toHaveTextContent(locationItem.fullName);
    expect(within(resultItems[1]).getByTestId('text-highlighter')).toHaveTextContent(propertyItem.fullName);
  });

  it('renders Luxury Offer badge only for property items with hasLuxOffer', () => {
    renderWithProviders();
    const input = screen.getByTestId('location-search-input');
    fireEvent.focus(input);
    fireEvent.change(input, { target: { value: inputValue } });
    const resultItems = screen.getAllByTestId('location-search-result');
    expect(within(resultItems[0]).queryByText('Luxury Offer')).not.toBeInTheDocument();
    expect(within(resultItems[1]).getByText('Luxury Offer')).toBeInTheDocument();
  });

  it('renders search results', () => {
    renderWithProviders();
    const input = screen.getByTestId('location-search-input');
    fireEvent.focus(input);
    fireEvent.change(input, { target: { value: inputValue } });
    expect(input).toBeInTheDocument();
    const resultItems = screen.getAllByTestId('location-search-result');
    expect(resultItems.length).toBeGreaterThan(0);
  });

  describe('when items have hasLuxOffer', () => {
    it('renders Luxury Offer badge', () => {
      renderWithProviders();
      const input = screen.getByTestId('location-search-input');
      fireEvent.focus(input);
      fireEvent.change(input, { target: { value: inputValue } });
      const resultItems = screen.getAllByTestId('location-search-result');
      expect(within(resultItems[1]).getByText('Luxury Offer')).toBeInTheDocument();
    });
  });

  describe('when items do not have hasLuxOffer', () => {
    const locationItem = { type: 'location', fullName: 'Melbourne, VIC, Australia' };
    const propertyItem = { type: 'property', fullName: 'Hilton Melbourne, Melbourne, Australia', hasLuxOffer: false };

    beforeEach(() => {
      useLocationData.mockReturnValue([locationItem, propertyItem]);
    });

    it('does not render Luxury Offer badge', () => {
      renderWithProviders();
      const input = screen.getByTestId('location-search-input');
      fireEvent.focus(input);
      fireEvent.change(input, { target: { value: inputValue } });
      const resultItems = screen.getAllByTestId('location-search-result');
      expect(within(resultItems[1]).queryByText('Luxury Offer')).not.toBeInTheDocument();
    });
  });
});
