import React from 'react';
import RecentSearch from './RecentSearch';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

const mockedPastSearch = {
  adults: 2,
  checkIn: '2024-04-01',
  checkOut: '2024-04-04',
  location: 'Rome, Italy',
  searchUrl: '/hotels/search/list?search-params',
};

const baseProps = {
  pastSearch: mockedPastSearch,
};

describe('RecentSearch', () => {
  it('renders without crashing', () => {
    render(<RecentSearch {...baseProps} />);
    expect(screen.getByText(mockedPastSearch.location)).toBeInTheDocument();
  });
});

describe('SearchLink', () => {
  it('has the correct href', () => {
    render(<RecentSearch {...baseProps} />);
    const link = screen.getByRole('link');
    expect(link).toHaveAttribute('href', mockedPastSearch.searchUrl + '&isRecentSearch=true');
  });

  describe('SearchParams', () => {
    it('renders the location', () => {
      render(<RecentSearch {...baseProps} />);
      expect(screen.getByText(mockedPastSearch.location)).toBeInTheDocument();
    });

    it('renders the dateRange', () => {
      render(<RecentSearch {...baseProps} />);
      expect(screen.getByText(/Apr 2024/)).toBeInTheDocument();
    });

    describe('renders the adults with correct label', () => {
      it('when there are more than one', () => {
        render(<RecentSearch {...baseProps} />);
        expect(screen.getByText(/2 Adults/)).toBeInTheDocument();
      });

      it('when there is ONE', () => {
        render(<RecentSearch pastSearch={{ ...mockedPastSearch, adults: 1 }} />);
        expect(screen.getByText(/1 Adult/)).toBeInTheDocument();
      });
    });

    describe('children', () => {
      describe('when there are No children in the search', () => {
        it('does NOT render their number', () => {
          render(<RecentSearch {...baseProps} />);
          expect(screen.queryByText(/Children/)).not.toBeInTheDocument();
          expect(screen.queryByText(/Child,/)).not.toBeInTheDocument();
        });
      });

      describe('when there are children in the search', () => {
        it('renders their number', () => {
          render(<RecentSearch pastSearch={{ ...mockedPastSearch, children: 2 }} />);
          expect(screen.getByText(/2 Children/)).toBeInTheDocument();
        });
      });

      describe('when there is ONE child in the search', () => {
        it('renders their number', () => {
          render(<RecentSearch pastSearch={{ ...mockedPastSearch, children: 1 }} />);
          expect(screen.getByText(/1 Child/)).toBeInTheDocument();
        });
      });
    });

    describe('infants', () => {
      describe('when there are No infants in the search', () => {
        it('does NOT render their number', () => {
          render(<RecentSearch {...baseProps} />);
          expect(screen.queryByText(/Infants/)).not.toBeInTheDocument();
          expect(screen.queryByText(/Infant,/)).not.toBeInTheDocument();
        });
      });

      describe('when there are infants in the search', () => {
        it('renders their number', () => {
          render(<RecentSearch pastSearch={{ ...mockedPastSearch, infants: 2 }} />);
          expect(screen.getByText(/2 Infants/)).toBeInTheDocument();
        });
      });

      describe('when there is ONE infant in the search', () => {
        it('renders their number', () => {
          render(<RecentSearch pastSearch={{ ...mockedPastSearch, infants: 1 }} />);
          expect(screen.getByText(/1 Infant/)).toBeInTheDocument();
        });
      });
    });
  });
});
