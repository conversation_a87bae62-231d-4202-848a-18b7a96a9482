import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { formatDates, SearchParams } from './helper';

const defaultProps = {
  title: 'children',
  value: 2,
  addComma: true,
};
const noCommaProps = {
  title: 'adults',
  value: 3,
  addComma: false,
};

describe('SearchParams', () => {
  describe('when addComma is true', () => {
    it('returns the component with the correct values', () => {
      render(<SearchParams {...defaultProps} />);
      expect(screen.getByTestId(defaultProps.title)).toHaveTextContent(', ' + defaultProps.value + ' ' + defaultProps.title);
    });
  });

  describe('when addComma is false', () => {
    it('returns the component with the correct values', () => {
      render(<SearchParams {...noCommaProps} />);
      expect(screen.getByTestId(noCommaProps.title)).toHaveTextContent(noCommaProps.value + ' ' + noCommaProps.title);
    });
  });
});

describe('formatDates', () => {
  describe('when the dates have the same month', () => {
    const checkIn = new Date('2030-03-21').setHours(0, 0, 0, 0);
    const checkOut = new Date('2030-03-25').setHours(0, 0, 0, 0);

    it('returns the date formatted as expected', () => {
      expect(formatDates(checkIn, checkOut)).toBe('21-25 Mar 2030');
    });
  });

  describe('when the dates have NOT the same month', () => {
    describe('and have the same year', () => {
      const checkIn = new Date('2030-03-31').setHours(0, 0, 0, 0);
      const checkOut = new Date('2030-04-03').setHours(0, 0, 0, 0);

      it('returns the date formatted as expected', () => {
        expect(formatDates(checkIn, checkOut)).toBe('31 Mar - 3 Apr 2030');
      });
    });

    describe('and have NOT the same year', () => {
      const checkIn = new Date('2030-12-31').setHours(0, 0, 0, 0);
      const checkOut = new Date('2031-01-03').setHours(0, 0, 0, 0);

      it('returns the date formatted as expected', () => {
        expect(formatDates(checkIn, checkOut)).toBe('31 Dec 2030 - 3 Jan 2031');
      });
    });
  });
});
