import React from 'react';
import { render, screen } from '@testing-library/react';
import SkeletonResults from './SkeletonResults';

const SkeletonCard = jest.fn((props) => <div data-testid="skeleton-card" key={props.index} {...props} />);

describe('<SkeletonResults />', () => {
  it('renders the correct number of items', () => {
    render(<SkeletonResults count={2} skeletonCardComponent={SkeletonCard} />);
    expect(screen.getAllByTestId('skeleton-card')).toHaveLength(2);
  });
});
