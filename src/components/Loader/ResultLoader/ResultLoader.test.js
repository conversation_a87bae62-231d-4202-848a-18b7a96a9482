import React from 'react';
import { render, screen } from '@testing-library/react';
import ResultLoader from './ResultLoader';
import SkeletonResults from './SkeletonResults';

const SkeletonCardComponent = jest.fn((props) => <div data-testid="skeleton-card" {...props} />);

jest.mock('./SkeletonResults', () => ({
  __esModule: true,
  default: jest.fn((props) => <div data-testid="skeleton-results" {...props} />),
}));

describe('<ResultLoader />', () => {
  const baseProps = {
    skeletonResultCount: 20,
    skeletonCardComponent: SkeletonCardComponent,
    children: <div data-testid="results"></div>,
  };

  it('displays the SkeletonResults with expected props when loading', () => {
    render(<ResultLoader {...baseProps} isLoading={true} />);
    const skeletonResults = screen.getByTestId('skeleton-results');
    expect(skeletonResults).toBeInTheDocument();
    expect(SkeletonResults).toHaveBeenCalledWith(
      expect.objectContaining({
        count: baseProps.skeletonResultCount,
        skeletonCardComponent: baseProps.skeletonCardComponent,
      }),
      expect.anything(),
    );
  });

  it('displays results when not loading', () => {
    render(<ResultLoader {...baseProps} isLoading={false} />);
    expect(screen.getByTestId('results')).toBeInTheDocument();
  });
});
