import React from 'react';
import { render, screen } from '@testing-library/react';
import Loader from './Loader';

const LoaderComponent = jest.fn(() => <div data-testid="skeleton" />);

describe('<Loader />', () => {
  const defaultChildren = <h1 data-testid="results">results!</h1>;

  const baseProps = {
    loaderComponent: LoaderComponent,
    skeletonResultCount: 20,
  };

  describe('when loading', () => {
    it('does not display results', () => {
      render(
        <Loader {...baseProps} isLoading={true}>
          {defaultChildren}
        </Loader>,
      );
      const results = screen.getByTestId('results');
      expect(results).not.toBeVisible();
    });

    it('displays skeleton results', () => {
      render(
        <Loader {...baseProps} isLoading={true}>
          {defaultChildren}
        </Loader>,
      );
      const skeleton = screen.getByTestId('skeleton');
      expect(skeleton).toBeInTheDocument();
      expect(skeleton).toBeVisible();
    });

    it('appends styling to the display results', () => {
      render(
        <Loader {...baseProps} isLoading={true} style={{ color: 'blue' }}>
          {defaultChildren}
        </Loader>,
      );
      const results = screen.getByTestId('results');
      expect(results.parentElement).toHaveStyle('color: blue');
    });

    it('appends styling to the skeleton results', () => {
      render(
        <Loader {...baseProps} isLoading={true} style={{ color: 'blue' }}>
          {defaultChildren}
        </Loader>,
      );
      const skeleton = screen.getByTestId('skeleton');
      expect(skeleton.parentElement).toHaveStyle('color: blue');
    });
  });

  describe('when not loading', () => {
    it('displays results', () => {
      render(
        <Loader {...baseProps} isLoading={false}>
          {defaultChildren}
        </Loader>,
      );
      const results = screen.getByTestId('results');
      expect(results).toBeVisible();
    });

    it('does not display skeleton results', () => {
      render(
        <Loader {...baseProps} isLoading={false}>
          {defaultChildren}
        </Loader>,
      );
      const SkeletonTransitionNode = screen.queryByTestId('skeleton').parentElement;
      expect(SkeletonTransitionNode).toHaveStyle('display: none');
    });
  });

  it('renders children with correct role and text', () => {
    render(
      <Loader {...baseProps} isLoading={false}>
        {defaultChildren}
      </Loader>,
    );
    expect(screen.getByRole('heading', { name: /results!/i })).toBeInTheDocument();
  });

  it('renders skeleton only if no children and loading', () => {
    render(<Loader {...baseProps} isLoading={true} />);
    expect(screen.queryByTestId('results')).not.toBeInTheDocument();
    expect(screen.getByTestId('skeleton')).toBeInTheDocument();
  });

  it('is accessible: results heading is focusable', async () => {
    render(
      <Loader {...baseProps} isLoading={false}>
        {defaultChildren}
      </Loader>,
    );
    const heading = screen.getByRole('heading', { name: /results!/i });
    heading.tabIndex = 0;
    heading.focus();
    expect(heading).toHaveFocus();
  });
});
