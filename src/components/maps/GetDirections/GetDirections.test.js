import React from 'react';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders } from 'test-utils/reactUtils';
import GetDirections from './GetDirections';
import { emitUserInteraction } from 'store/userEnvironment/userEnvironmentActions';

jest.mock('store/userEnvironment/userEnvironmentActions', () => ({
  emitUserInteraction: jest.fn(),
}));

const defaultProps = {
  address: {
    streetAddress: ['Arrival Drive, Melbourne Airport'],
    suburb: 'Tullamarine',
  },
};

describe('<GetDirections />', () => {
  it('renders a link with the correct href and attributes', () => {
    renderWithProviders(<GetDirections {...defaultProps} />);
    const link = screen.getByRole('link', { name: /get directions on google maps/i });
    expect(link).toHaveAttribute(
      'href',
      'http://www.google.com/maps/dir/?api=1&destination=Arrival Drive, Melbourne Airport, Tullamarine&travelmode=driving',
    );
    expect(link).toHaveAttribute('target', '_blank');
    expect(link).toHaveAttribute('rel', 'noopener noreferrer');
    expect(link).toHaveAttribute('aria-label', 'Get directions on Google Maps');
  });

  it('emits a gtm event when clicked', async () => {
    renderWithProviders(<GetDirections {...defaultProps} />);
    const link = screen.getByRole('link', { name: /get directions on google maps/i });
    await userEvent.click(link);
    expect(emitUserInteraction).toHaveBeenCalledWith({ type: 'Directions Pop Up', value: 'Get Directions Link Selected' });
  });
});
