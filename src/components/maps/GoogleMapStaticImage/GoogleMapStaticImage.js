import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import qs from 'query-string';
import castArray from 'lodash/castArray';
import zipWith from 'lodash/zipWith';
import reverse from 'lodash/reverse';
import compact from 'lodash/compact';
import { GOOGLE_MAPS_API_KEY, GOOGLE_MAPS_STATIC_API_URL } from 'config';
import { BREAK_POINTS_PX } from 'lib/theme';
import { Image, Box } from '@qga/roo-ui/components';
import genericMap from './map-generic.svg';

const getImageUrl = (query) => `${GOOGLE_MAPS_STATIC_API_URL}?${qs.stringify(query)}`;

// Location is string of either a city name or lat/lng coordinates. This checks if either lat/lng are undefined
const isLatOrLngUndefinedRegex = new RegExp(/undefined/);

const buildImageSources = ({ widths, heights }) => {
  const breakpoints = [0, ...BREAK_POINTS_PX];
  const sources = zipWith(widths, heights, breakpoints, (width, height, breakpoint) =>
    width && height ? { width, height, breakpoint } : null,
  );
  return reverse(compact(sources));
};

const GoogleMapStaticImage = ({ location, width, height, zoom, format, markers, locationName }) => {
  const [hasBrokenImage, setHasBrokenImage] = useState(false);

  const alt = `map of ${locationName || location}`;
  const defaultQuery = {
    center: location,
    zoom: markers ? 16 : zoom,
    format: format,
    markers: markers ? location : undefined,
    key: GOOGLE_MAPS_API_KEY,
    style: 'feature:poi.business|visibility:off',
  };

  const imageUrl = ({ width, height }) => {
    const baseUrl = getImageUrl({ ...defaultQuery, size: `${width}x${height}` });
    return `${baseUrl} 1x, ${baseUrl}&scale=2 2x`;
  };

  const setImageError = () => {
    setHasBrokenImage(true);
  };

  useEffect(() => {
    if (isLatOrLngUndefinedRegex.test(location)) {
      setHasBrokenImage(true);
    }
  }, [location, setHasBrokenImage]);

  const imageSizes = buildImageSources({ widths: castArray(width), heights: castArray(height) });
  const fallbackImage = imageSizes[0];

  return (
    <Box position="relative" color="greys.alto" mb={4} mt={[4, 0]}>
      {!hasBrokenImage && (
        <picture data-testid="picture" onError={setImageError}>
          {imageSizes.map(({ width, height, breakpoint }) => (
            <source key={breakpoint} srcSet={imageUrl({ width, height })} media={`(min-width: ${breakpoint}px)`} data-testid="source" />
          ))}
          <Image
            maxWidth="100%"
            width="100%"
            src={imageUrl({ width: fallbackImage.width, height: fallbackImage.height })}
            alt={alt}
            data-testid="google-maps-static-image"
          />
        </picture>
      )}
      {hasBrokenImage && <Image maxWidth="100%" width="100%" src={genericMap} alt={alt} data-testid="generic-static-image" />}
    </Box>
  );
};

GoogleMapStaticImage.propTypes = {
  location: PropTypes.string.isRequired,
  width: PropTypes.oneOfType([PropTypes.array, PropTypes.number]),
  height: PropTypes.oneOfType([PropTypes.array, PropTypes.number]),
  zoom: PropTypes.number,
  format: PropTypes.oneOf(['png8', 'png32', 'gif', 'jpg']),
  markers: PropTypes.bool,
  locationName: PropTypes.string,
};

GoogleMapStaticImage.defaultProps = {
  width: 400,
  height: 400,
  zoom: 12,
  format: 'png8',
  markers: false,
  locationName: '',
};

export default GoogleMapStaticImage;
