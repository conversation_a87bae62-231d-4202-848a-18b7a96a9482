import React from 'react';
import { render, screen } from '@testing-library/react';
import GoogleMapStaticImage from './GoogleMapStaticImage';

jest.mock('config', () => ({
  GOOGLE_MAPS_API_KEY: 'skeleton_key',
  GOOGLE_MAPS_STATIC_API_URL: 'google_maps_url',
}));

const defaultProps = {
  location: 'Melbourne,AUS',
  width: 400,
  height: 200,
  zoom: 10,
  format: 'png8',
  style: 'feature:poi.business|visibility:off',
};

const renderMap = (props = {}) => {
  render(<GoogleMapStaticImage {...defaultProps} {...props} />);
};

describe('<GoogleMapStaticImage />', () => {
  describe('with a single width and height', () => {
    beforeEach(() => {
      renderMap();
    });

    it('uses the config key in image url', () => {
      expect(screen.getByRole('img').src).toMatch('key=skeleton_key');
    });

    it('passes props to the image url', () => {
      const img = screen.getByRole('img');
      expect(img.src).toMatch('center=Melbourne%2CAUS');
      expect(img.src).toMatch('zoom=10');
      expect(img.src).toMatch('format=png8');
      expect(img.src).toMatch('style=feature%3Apoi.business%7Cvisibility%3Aoff');
    });

    it('derives a size param and passes it to the image url', () => {
      expect(screen.getByRole('img').src).toMatch('size=400x200');
    });

    it('uses the correct url for the static maps api', () => {
      expect(screen.getByRole('img').src).toMatch('google_maps_url');
    });

    it('adds src set for hdpi displays', () => {
      const sources = screen.queryAllByTestId('source');
      const expectedUrl =
        'google_maps_url?center=Melbourne%2CAUS&format=png8&key=skeleton_key&size=400x200&style=feature%3Apoi.business%7Cvisibility%3Aoff&zoom=10';
      expect(sources[0].srcset).toEqual(`${expectedUrl} 1x, ${expectedUrl}&scale=2 2x`);
    });

    it('adds the correct alt tag to the image when an locationName property is supplied', () => {
      renderMap({ locationName: 'The Hotel' });
      expect(screen.getByRole('img', { name: 'map of The Hotel' })).toBeInTheDocument();
    });

    it('adds the correct alt tag to the image when an alt property is not supplied', () => {
      expect(screen.getByRole('img', { name: 'map of Melbourne,AUS' })).toBeInTheDocument();
    });
  });

  describe('with multiple widths and heights', () => {
    beforeEach(() => {
      renderMap({ width: [100, 200], height: [100, 200] });
    });

    it('creates multiple sources with the correct sizes', () => {
      const sources = screen.queryAllByTestId('source');
      expect(sources).toHaveLength(2);
      expect(sources[0].srcset).toMatch('size=200x200');
      expect(sources[0].media).toEqual('(min-width: 768px)');
      expect(sources[1].srcset).toMatch('size=100x100');
      expect(sources[1].media).toEqual('(min-width: 0px)');
    });

    it('passes props to the image sources', () => {
      const sources = screen.queryAllByTestId('source');
      sources.forEach((source) => {
        expect(source.srcset).toMatch('center=Melbourne%2CAUS');
        expect(source.srcset).toMatch('zoom=10');
        expect(source.srcset).toMatch('format=png8');
        expect(source.srcset).toMatch('style=feature%3Apoi.business%7Cvisibility%3Aoff');
        expect(source.srcset).toMatch('scale=2 2x');
      });
    });

    it('uses the largest breakpoint for the IE fallback', () => {
      expect(screen.getByRole('img').src).toMatch('size=200x200');
    });
  });

  describe('when the markers prop is not present', () => {
    it('does not pass the markers prop into the image url', () => {
      renderMap();
      expect(screen.getByRole('img').src).not.toMatch('markers=Melbourne%2CAUS');
    });

    it('return the zoom passed as prop in ImageQuery', () => {
      renderMap({ zoom: 10 });
      expect(screen.getByRole('img', { name: 'map of Melbourne,AUS' }).src).toMatch('zoom=10');
    });
  });

  describe('when the markers prop is present', () => {
    beforeEach(() => {
      renderMap({ markers: true });
    });

    it('return the location as value for the markers key and zoom of 16 in ImageQuery', () => {
      expect(screen.getByRole('img').src).toMatch('markers=Melbourne%2CAUS');
    });

    it('return the zoom of 16 in ImageQuery', () => {
      expect(screen.getByRole('img').src).toMatch('zoom=16');
    });
  });

  describe('when the image url is broken', () => {
    const latitude = '123';
    const longitude = '456';

    it('renders the generic image', () => {
      // Use an invalid location to trigger the fallback
      renderMap({ location: `${undefined},456` });
      expect(screen.getByTestId('generic-static-image')).toBeInTheDocument();
    });

    it('when invalid latitude is provided', () => {
      renderMap({ location: `${undefined},${longitude}` });
      expect(screen.getByTestId('generic-static-image')).toBeInTheDocument();
    });

    it('when invalid longitude is provided', () => {
      renderMap({ location: `${latitude},${undefined}` });
      expect(screen.getByTestId('generic-static-image')).toBeInTheDocument();
    });
  });
});
