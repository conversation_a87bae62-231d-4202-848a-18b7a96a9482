import React from 'react';
import styled from '@emotion/styled';
import { color } from 'styled-system';
import { LoadingIndicator as InnerLoadingIndicator } from '@qga/roo-ui/components';

const Wrapper = styled.div`
  ${color}
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  align-items: center;
  z-index: 1;
`;

const LoadingIndicator = () => (
  <Wrapper backgroundColor="greys.porcelain" data-testid="loading-indicator">
    <InnerLoadingIndicator />
  </Wrapper>
);
export default LoadingIndicator;
