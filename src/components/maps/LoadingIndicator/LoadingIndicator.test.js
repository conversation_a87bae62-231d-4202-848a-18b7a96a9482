import React from 'react';
import { render, screen } from '@testing-library/react';
import LoadingIndicator from './LoadingIndicator';

describe('<LoadingIndicator />', () => {
  it('renders without throwing', () => {
    expect(() => render(<LoadingIndicator />)).not.toThrow();
  });

  it('renders the correct background color', () => {
    render(<LoadingIndicator />);
    const wrapper = screen.getByTestId('loading-indicator');
    expect(wrapper).toBeInTheDocument();
    expect(wrapper).toHaveStyleRule('background-color', 'greys.porcelain');
  });
});
