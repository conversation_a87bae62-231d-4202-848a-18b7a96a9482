import React from 'react';
import { render, screen } from '@testing-library/react';
import GoogleMap from './GoogleMap';
import { hasBounds, positionMapToCenter, fitBounds } from './mapUtils';
import { getQueryNeLat, getQuerySwLat, getQueryNeLng, getQuerySwLng } from 'store/router/routerSelectors';
import { captureErrorInSentry } from 'lib/errors';
import mockStore from 'redux-mock-store';
import { Provider } from 'react-redux';
import { ThemeProvider } from 'emotion-theming';
import { theme } from '@qga/roo-ui';

const mockHandlerRef = React.createRef();
jest.mock('google-map-react', () => {
  const React = require('react');
  return React.forwardRef(({ children, ...props }, ref) => {
    React.useImperativeHandle(ref || mockHandlerRef, () => props);
    return <div data-testid="google-map-react">{children}</div>;
  });
});
jest.mock('./mapUtils');
jest.mock('store/router/routerSelectors');
jest.mock('lib/errors');

function LatLngBounds() {
  this.extend = jest.fn();
  this.toJSON = jest.fn();
}

const gMap = { panToBounds: jest.fn() };
const neLat = 1;
const swLat = 2;
const neLng = 3;
const swLng = 4;
const center = { lat: 123, lng: 456 };

const baseProps = {
  center: { lat: 123, lng: 456 },
};

const mapsChild = <div data-testid="maps-child" />;

const configureMockStore = mockStore([]);
const store = configureMockStore({});

const renderWithProviders = (props = {}) => {
  const allProps = { ...baseProps, ...props };
  return render(
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <GoogleMap ref={mockHandlerRef} {...allProps}>
          {mapsChild}
        </GoogleMap>
      </ThemeProvider>
    </Provider>,
  );
};

afterEach(() => {
  positionMapToCenter.mockClear();
  hasBounds.mockClear();
  fitBounds.mockClear();
});

describe('<GoogleMap />', () => {
  it('renders the map', () => {
    renderWithProviders();
    expect(screen.getByTestId('google-map-react')).toBeInTheDocument();
  });

  it('renders the <LoadingIndicator />', () => {
    renderWithProviders({ center: undefined });
    expect(screen.getByTestId('loading-indicator')).toBeInTheDocument();
  });

  it('renders the <PageError />', () => {
    renderWithProviders({ error: true });
    expect(screen.getByTestId('error-body')).toBeInTheDocument();
  });

  it('renders the children', () => {
    renderWithProviders();
    const mapComponent = mockHandlerRef.current;
    mapComponent.onGoogleApiLoaded({ map: gMap, maps: { LatLngBounds } });
    expect(screen.getByTestId('maps-child')).toBeInTheDocument();
  });

  it('logs google map loading error to sentry', () => {
    renderWithProviders();
    const mapComponent = mockHandlerRef.current;
    mapComponent.onGoogleApiLoaded({});
    expect(captureErrorInSentry).toHaveBeenCalledWith(new Error('Google Map has failed to load'));
  });

  describe('centering map on load', () => {
    describe('with bounds', () => {
      it('fits to bounds', () => {
        hasBounds.mockReturnValue(true);
        getQueryNeLat.mockReturnValue(neLat);
        getQuerySwLat.mockReturnValue(swLat);
        getQueryNeLng.mockReturnValue(neLng);
        getQuerySwLng.mockReturnValue(swLng);

        renderWithProviders();
        const mapComponent = mockHandlerRef.current;
        mapComponent.onGoogleApiLoaded({ map: gMap, maps: { LatLngBounds } });
        expect(fitBounds).toHaveBeenCalledWith(gMap, {
          north: Number(neLat),
          south: Number(swLat),
          east: Number(neLng),
          west: Number(swLng),
        });
        expect(positionMapToCenter).not.toHaveBeenCalled();
      });
    });

    describe('without bounds', () => {
      it('centers map', () => {
        hasBounds.mockReturnValue(false);
        renderWithProviders();
        const mapComponent = mockHandlerRef.current;
        mapComponent.onGoogleApiLoaded({ map: gMap, maps: { LatLngBounds } });
        expect(positionMapToCenter).toHaveBeenCalledWith(gMap, center);
        expect(fitBounds).not.toHaveBeenCalled();
      });
    });
  });

  describe('map callbacks', () => {
    it('fires onMapAPILoaded once the map has loaded', () => {
      const onMapAPILoaded = jest.fn();
      renderWithProviders({ onMapAPILoaded });
      const gMaps = { LatLngBounds };
      const mapComponent = mockHandlerRef.current;
      mapComponent.onGoogleApiLoaded({ map: gMap, maps: gMaps });
      expect(onMapAPILoaded).toHaveBeenCalledWith({ gMap, gMaps });
    });

    it('fires onMapTilesLoaded once the map tiles have rendered', () => {
      const onMapTilesLoaded = jest.fn();
      renderWithProviders({ onMapTilesLoaded });
      const mapComponent = mockHandlerRef.current;
      mapComponent.onTilesLoaded();
      expect(onMapTilesLoaded).toHaveBeenCalledWith(true);
    });

    it('does not fire onMapTilesLoaded if the map is already ready and rendered', () => {
      const onMapTilesLoaded = jest.fn();
      renderWithProviders({ onMapTilesLoaded, ready: true });
      const mapComponent = mockHandlerRef.current;
      mapComponent.onTilesLoaded();
      expect(onMapTilesLoaded).not.toHaveBeenCalled();
    });

    it('fires onClick', () => {
      const onClick = jest.fn();
      renderWithProviders({ onClick });
      const mapComponent = mockHandlerRef.current;
      mapComponent.onClick({ id: 1, lat: 123, lng: 456 });
      expect(onClick).toHaveBeenCalledWith({ id: 1, lat: 123, lng: 456 });
    });

    it('fires onMapMoved when map is zoomed', () => {
      const onMapMoved = jest.fn();
      renderWithProviders({ onMapMoved });
      const mapComponent = mockHandlerRef.current;
      mapComponent.onZoomAnimationStart();
      expect(onMapMoved).toHaveBeenCalledWith();
    });

    it('fires onMapMoved when map is panned', () => {
      const onMapMoved = jest.fn();
      renderWithProviders({ onMapMoved });
      const mapComponent = mockHandlerRef.current;
      mapComponent.onDragEnd();
      expect(onMapMoved).toHaveBeenCalledWith();
    });
  });
});
