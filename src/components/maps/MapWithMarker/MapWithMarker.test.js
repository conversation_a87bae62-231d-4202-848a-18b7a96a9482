import React from 'react';
import { render, screen } from '@testing-library/react';
import MapWithMarker from './MapWithMarker';
import GoogleMap from 'components/maps/GoogleMap';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { useInView } from 'react-intersection-observer';

jest.mock('components/maps/GoogleMap', () => jest.fn(({ children }) => <div data-testid="google-map">{children}</div>));
jest.mock('components/maps/GoogleMapMarker', () => {
  const GoogleMapMarkerMock = jest.fn(() => <div data-testid="google-map-marker" />);
  return {
    __esModule: true,
    default: GoogleMapMarkerMock,
    GoogleMapMarkerMock,
  };
});
jest.mock('hooks/useBreakpoints', () => ({
  useBreakpoints: jest.fn(),
}));
jest.mock('react-intersection-observer', () => ({
  useInView: jest.fn(),
}));

const mapError = jest.fn();

const baseProps = {
  longitude: 123,
  latitude: 456,
  name: 'test-map',
  onError: mapError,
};

const mapOptions = {
  gestureHandling: 'greedy',
  disableDefaultUI: false,
  zoom: 16,
  center: { lat: baseProps.latitude, lng: baseProps.longitude },
  mapTypeControl: true,
  streetViewControl: true,
  styles: [
    { featureType: 'poi.place_of_worship', stylers: [{ visibility: 'off' }] },
    { featureType: 'poi.business', stylers: [{ visibility: 'off' }] },
    { featureType: 'poi.government', stylers: [{ visibility: 'off' }] },
    { featureType: 'poi.medical', stylers: [{ visibility: 'off' }] },
    { featureType: 'poi.school', stylers: [{ visibility: 'off' }] },
  ],
};

describe('<MapWithMarker />', () => {
  let GoogleMapMarkerMock;
  beforeEach(() => {
    GoogleMapMarkerMock = require('components/maps/GoogleMapMarker').GoogleMapMarkerMock;
    GoogleMap.mockClear();
    GoogleMapMarkerMock.mockClear();
    useBreakpoints.mockImplementation(() => ({
      isLessThanBreakpoint: () => false,
    }));
    useInView.mockImplementation(() => [React.createRef(), true]);
  });

  it('renders GoogleMap with correct props', () => {
    render(<MapWithMarker {...baseProps} />);
    expect(GoogleMap).toHaveBeenCalledWith(
      expect.objectContaining({
        name: baseProps.name,
        options: mapOptions,
        onError: mapError,
      }),
      expect.anything(),
    );
    expect(screen.getByTestId('google-map')).toBeInTheDocument();
  });

  it('renders GoogleMapMarker with correct props', () => {
    render(<MapWithMarker {...baseProps} />);
    expect(GoogleMapMarkerMock).toHaveBeenCalledWith(
      expect.objectContaining({ lat: baseProps.latitude, lng: baseProps.longitude }),
      expect.anything(),
    );
    expect(screen.getByTestId('google-map-marker')).toBeInTheDocument();
  });

  describe('on a phone', () => {
    beforeEach(() => {
      useBreakpoints.mockImplementation(() => ({
        isLessThanBreakpoint: () => true,
      }));
    });

    it('renders GoogleMap with phone options', () => {
      render(<MapWithMarker {...baseProps} />);
      expect(GoogleMap).toHaveBeenCalledWith(
        expect.objectContaining({
          name: baseProps.name,
          options: { ...mapOptions, disableDefaultUI: true },
        }),
        expect.anything(),
      );
    });
  });

  describe('when lazy loading', () => {
    it('should render a placeholder component when `inView` is falsey', () => {
      useInView.mockImplementation(() => [React.createRef(), false]);
      render(<MapWithMarker {...baseProps} lazy />);
      expect(screen.getByTestId('google-map-placeholder')).toBeInTheDocument();
    });

    it('should render the map component when `inView` is truthy', () => {
      useInView.mockImplementation(() => [React.createRef(), true]);
      render(<MapWithMarker {...baseProps} lazy />);
      expect(screen.queryByTestId('google-map-placeholder')).not.toBeInTheDocument();
      expect(screen.getByTestId('google-map')).toBeInTheDocument();
    });
  });
});
