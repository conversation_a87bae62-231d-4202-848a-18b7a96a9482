import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import LegacyBrowserBoundary from './LegacyBrowserBoundary';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';
jest.mock('store/userEnvironment/userEnvironmentSelectors');

const mockStore = configureStore([]);
const renderComponent = (props = {}) => {
  const store = mockStore({});
  render(
    <Provider store={store}>
      <LegacyBrowserBoundary {...props} />
    </Provider>,
  );
};

const children = 'modern browsers only';

describe('<LegacyBrowserBoundary />', () => {
  it('does not render children on legacy browsers', () => {
    getBrowser.mockReturnValue({ name: 'Internet Explorer' });
    renderComponent({ children });
    expect(screen.queryByText(children)).not.toBeInTheDocument();
  });

  it('renders children on modern browsers', () => {
    getBrowser.mockReturnValue({ name: 'Chrome' });
    renderComponent({ children });
    expect(screen.getByText(children)).toBeInTheDocument();
  });

  it('renders children on a different modern browser like Firefox', () => {
    getBrowser.mockReturnValue({ name: 'Firefox' });
    renderComponent({ children });
    expect(screen.getByText(children)).toBeInTheDocument();
  });
});
