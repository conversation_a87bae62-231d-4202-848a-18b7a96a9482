import React, { createRef, useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { Box, Flex, Container, NakedButton, Link } from '@qga/roo-ui/components';
import { PropTypes } from 'prop-types';
import startCase from 'lodash/startCase';
import { useDataLayer } from 'hooks/useDataLayer';
import { mediaQuery } from 'lib/styledSystem';
import { getIsExclusive } from 'store/property/propertySelectors';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';
import { isLegacyBrowser, scrollTo, getOffsetTop } from 'lib/browser';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { calculateMenuOffset } from './calculateMenuOffset';

const intersectionArea = '-20% 0px -80% 0px';

const Wrapper = styled(Box)`
  background: white;
  left: 0;
  right: 0;
  top: -1px;

  transition: opacity 0.25s ease-in-out;
  opacity: 1;

  white-space: nowrap;
  overflow-x: scroll;
  overflow-y: hidden;
  ::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none; /* Firefox 64 */
  box-shadow: ${(props) => (props.isExclusive ? '0px 1px 15px rgba(0, 0, 0, 0.10)' : 'none')};
  border-bottom: 1px solid;
  border-color: ${themeGet('colors.greys.alto')};

  ${mediaQuery.minWidth.sm} {
    margin-bottom: ${(props) => (props.isExclusive ? 0 : themeGet('space.8'))};
    box-shadow: ${(props) => (props.isExclusive ? '0px 1px 15px rgba(0, 0, 0, 0.10)' : props.shadow ? props.theme.shadows.result : 'none')};
  }

  ${mediaQuery.minWidth.md} {
    margin-bottom: ${themeGet('space.8')};
    box-shadow: ${(props) => (props.shadow ? props.theme.shadows.result : 'none')};
  }
`;

const NavLink = styled(NakedButton)`
  margin: 0 ${themeGet('space.3')};

  background: transparent;
  color: ${themeGet('colors.greys.charcoal')};
  font-size: ${themeGet('fontSizes.base')};
  border-bottom: 3px solid;
  border-color: ${(props) => (props.isActive ? themeGet('colors.brand.primary') : 'transparent')};
  transition: border-color 0.2s ease-in-out;
  width: inherit;
  display: inline-block;

  &:hover,
  &:active {
    color: ${themeGet('colors.greys.charcoal')};
  }

  &:focus {
    outline: ${themeGet('focus.outline')};
  }

  ${mediaQuery.minWidth.sm} {
    margin: 0 ${themeGet('space.4')};
  }

  ${mediaQuery.minWidth.md} {
    &:hover {
      border-color: ${themeGet('colors.brand.primary')};
    }
  }
`;

const InPageNavBar = ({ menuItems = [], onMenuChanged, shadow, justifyContent = 'flex-start', menuRefPositionOffset, ...rest }) => {
  const isExclusive = useSelector(getIsExclusive);
  const browser = useSelector(getBrowser);
  const { isLessThanBreakpoint } = useBreakpoints();

  const isMobile = isLessThanBreakpoint(0);
  const legacyBrowser = isLegacyBrowser(browser?.name);

  // Only display isExclusive flagged items if the offer is exclusive
  const parsedMenuItems = menuItems.filter(({ isExclusive: isItemExclusive }) => isExclusive || !isItemExclusive);
  const [activeSection, setActiveSection] = useState(menuItems?.[0]);
  const menuRef = createRef();

  const { emitInteractionEvent } = useDataLayer();

  const updateMenuOffset = useCallback(() => {
    if (!isMobile) return;

    const offset = calculateMenuOffset(parsedMenuItems, activeSection.name);

    menuRef.current.scrollLeft = offset;
  }, [activeSection, isMobile, menuRef, parsedMenuItems]);

  const handleOnClick = useCallback(
    (navId) => () => {
      const menuItem = parsedMenuItems.find((item) => item.name === navId);

      if (activeSection !== menuItem?.name) {
        setActiveSection(menuItem);
        onMenuChanged(menuItem);
        scrollTo({
          behavior: 'smooth',
          top: getOffsetTop(menuItem.ref.current) - menuRefPositionOffset,
        });
      }

      emitInteractionEvent({ type: 'In Page Navigation', value: `${startCase(menuItem.text)} Selected` });
    },
    [parsedMenuItems, emitInteractionEvent], // eslint-disable-line react-hooks/exhaustive-deps
  );

  useEffect(() => {
    updateMenuOffset();
  }, [activeSection, updateMenuOffset]);

  useEffect(() => {
    const handleIntersection = (entries = []) => {
      const intersection = entries.find((entry) => entry.isIntersecting === true);
      if (!intersection) return;

      const menuItem = parsedMenuItems.find((item) => item.ref.current === intersection.target);
      setActiveSection(menuItem);
    };
    const observer = new IntersectionObserver(handleIntersection, { rootMargin: intersectionArea });
    parsedMenuItems.forEach((item) => {
      if (item.ref?.current) {
        observer.observe(item.ref.current);
      }
    });
    return () => observer.disconnect(); // disconnect on unmount
  }, [parsedMenuItems]);

  if (legacyBrowser) return null;

  return (
    <Wrapper
      position="sticky"
      role="navigation"
      shadow={shadow}
      zIndex="stickyNavigation"
      isExclusive={isExclusive}
      ref={menuRef}
      {...rest}
    >
      <Container>
        <Flex justifyContent={justifyContent} pr={4}>
          {parsedMenuItems.map(({ name, text, linkRef }) => (
            <NavLink
              role="link"
              key={name}
              as={Link}
              onClick={handleOnClick(name)}
              py={[3, 4]}
              isActive={activeSection?.name === name}
              ref={linkRef}
              tabIndex={0}
            >
              {text}
            </NavLink>
          ))}
        </Flex>
      </Container>
    </Wrapper>
  );
};

InPageNavBar.displayName = 'InPageNavBar';

InPageNavBar.propTypes = {
  menuItems: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      linkRef: PropTypes.oneOfType([
        PropTypes.func,
        PropTypes.shape({
          current: PropTypes.oneOfType([PropTypes.object, PropTypes.element]),
        }),
      ]),
      ref: PropTypes.oneOfType([
        PropTypes.func,
        PropTypes.shape({
          current: PropTypes.oneOfType([PropTypes.object, PropTypes.element]),
        }),
      ]).isRequired,
      text: PropTypes.string.isRequired,
    }),
  ),
  onMenuChanged: PropTypes.func,
  menuHeight: PropTypes.number,
  menuRefPositionOffset: PropTypes.number,
  shadow: PropTypes.bool,
  justifyContent: PropTypes.oneOfType([PropTypes.string, PropTypes.array]),
};

InPageNavBar.defaultProps = {
  menuRefPositionOffset: 120,
  onMenuChanged: () => {},
};

export default InPageNavBar;
