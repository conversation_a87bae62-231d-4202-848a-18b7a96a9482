import React, { createRef } from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useInView } from 'react-intersection-observer';
import '@testing-library/jest-dom';
import InPageNavBar from './InPageNavBar';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { useDataLayer } from 'hooks/useDataLayer';
import { isLegacyBrowser, scrollTo, getOffsetTop } from 'lib/browser';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';

import { calculateMenuOffset } from './calculateMenuOffset';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';

jest.mock('react', () => {
  const originReact = jest.requireActual('react');
  const mockCreateRef = jest.fn();
  return {
    ...originReact,
    createRef: mockCreateRef,
  };
});

jest.mock('./calculateMenuOffset');
jest.mock('hooks/useBreakpoints');
jest.mock('hooks/useDataLayer');

jest.mock('react-intersection-observer');
jest.mock('lib/browser', () => ({
  scrollTo: jest.fn(),
  isLegacyBrowser: jest.fn(),
  getOffsetTop: jest.fn(),
}));
jest.mock('store/userEnvironment/userEnvironmentSelectors');

const emitInteractionEvent = jest.fn();
const isLessThanBreakpoint = jest.fn();
const onMenuChanged = jest.fn();

document.body.innerHTML = '<div id="mock"/>';
const mockedElement = {
  ...document.getElementById('mock'),
  offsetTop: 50,
};
mockedElement.getBoundingClientRect = jest.fn().mockReturnValue({ top: 200 });

document.body.innerHTML += '<div id="location-mock"/>';
const locationMockedElement = {
  ...document.getElementById('location-mock'),
  offsetTop: 100,
};
locationMockedElement.getBoundingClientRect = jest.fn().mockReturnValue({ top: 300 });

let intersection;
useInView.mockImplementation(() => [React.createRef(), intersection]);
IntersectionObserver.prototype.observe = jest.fn();

const menuRef = {
  current: {
    scrollLeft: 0,
  },
};
const linkRef = {
  current: {
    scrollWidth: 50,
  },
};

const menuHeight = 10;

const mockStore = configureStore([]);

const renderComponent = (props = {}) => {
  const store = mockStore({});
  return render(
    <Provider store={store}>
      <InPageNavBar onMenuChanged={onMenuChanged} {...props} />
    </Provider>,
  );
};

const menuItems = [
  { name: 'photos', text: 'Photos', linkRef: linkRef, ref: { current: mockedElement } },
  { name: 'rooms', text: 'Rooms', linkRef: linkRef, ref: { current: mockedElement } },
  { name: 'about', text: 'About this property', linkRef: linkRef, ref: { current: mockedElement } },
  { name: 'location', text: 'Location', linkRef: linkRef, ref: { current: locationMockedElement } },
  { name: 'policies', text: 'Property policies', linkRef: linkRef, ref: { current: mockedElement } },
];

beforeEach(() => {
  jest.clearAllMocks();
  useBreakpoints.mockReturnValue({ isLessThanBreakpoint });
  calculateMenuOffset.mockReturnValue(50);
  createRef.mockReturnValue(menuRef);
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  isLegacyBrowser.mockReturnValue(false);
  getBrowser.mockReturnValue({ name: 'browser name' });
  getOffsetTop.mockReturnValue(290);
});

describe('<InPageNavBar />', () => {
  describe('with a legacy browser', () => {
    beforeEach(() => {
      isLegacyBrowser.mockReturnValue(true);
    });

    it('does not render the navigation bar', () => {
      renderComponent({ menuItems, menuHeight });
      expect(screen.queryByRole('navigation')).not.toBeInTheDocument();
    });
  });

  it('renders all menu items with correct text and links', () => {
    renderComponent({ menuItems, menuHeight });

    expect(screen.getAllByRole('link')).toHaveLength(menuItems.length);
    expect(screen.getByRole('link', { name: /photos/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /rooms/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /about this property/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /location/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /property policies/i })).toBeInTheDocument();
  });

  it('renders the navigation bar with correct sticky positioning', () => {
    renderComponent();
    const navBar = screen.getByRole('navigation');

    expect(navBar).toHaveStyle('position: sticky');
    expect(navBar).toHaveStyle('z-index: stickyNavigation');
  });

  describe('when clicking a link', () => {
    const defaultOffset = 120;
    const customOffset = 80;

    it('has a default menuRefPositionOffset of 120', async () => {
      renderComponent({ menuItems, menuRef: menuRef });
      const aboutButton = screen.getByRole('link', { name: /about this property/i });
      await userEvent.click(aboutButton);
      expect(scrollTo).toHaveBeenCalledWith({
        behavior: 'smooth',
        top: 290 - defaultOffset,
      });
    });

    it('uses a custom menuRefPositionOffset when provided', async () => {
      renderComponent({ menuItems, menuRefPositionOffset: customOffset, menuRef: menuRef });
      const aboutButton = screen.getByRole('link', { name: /about this property/i });
      await userEvent.click(aboutButton);

      expect(scrollTo).toHaveBeenCalledWith({
        behavior: 'smooth',
        top: 290 - customOffset,
      });
    });

    it('emits an event to the data layer', async () => {
      renderComponent({ menuItems, menuRef });
      const aboutButton = screen.getByRole('link', { name: /about this property/i });
      await userEvent.click(aboutButton);
      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'In Page Navigation',
        value: 'About This Property Selected',
      });
    });

    it('calls onMenuChanged with the correct menu item', async () => {
      renderComponent({ menuItems, menuRef });
      const aboutButton = screen.getByRole('link', { name: /about this property/i });
      await userEvent.click(aboutButton);
      expect(onMenuChanged).toHaveBeenCalledWith(menuItems[2]);
    });
  });

  describe('when the active section is updated', () => {
    describe('and with a mobile viewport', () => {
      beforeEach(() => {
        isLessThanBreakpoint.mockReturnValue(true);
      });

      it('calls calculateMenuOffset and scrolls the menu into view when a link is clicked', async () => {
        renderComponent({ menuItems, menuRef });
        const aboutButton = screen.getByRole('link', { name: /about this property/i });
        await userEvent.click(aboutButton);

        expect(calculateMenuOffset).toHaveBeenCalledTimes(2);
        expect(calculateMenuOffset).toHaveBeenCalledWith(menuItems, 'about');
        expect(menuRef.current.scrollLeft).toEqual(50);
      });
    });

    describe('and with a desktop viewport', () => {
      beforeEach(() => {
        isLessThanBreakpoint.mockReturnValue(false);
      });

      it('does NOT call calculateMenuOffset or scroll the menu when a link is clicked', async () => {
        renderComponent({ menuItems });

        const aboutButton = screen.getByRole('link', { name: /about this property/i });
        await userEvent.click(aboutButton);

        expect(calculateMenuOffset).not.toHaveBeenCalled();
        expect(menuRef.current.scrollLeft).toEqual(0);
      });
    });
  });
});
