import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ListSearchLayout from './ListSearchLayout';
import { mocked } from 'test-utils';
import LegacyBrowserBoundary from 'components/LegacyBrowserBoundary';
import { getIsMobileApp } from 'store/ui/uiSelectors';
import { useRouter } from 'next/router';
import { useBreakpoints } from 'hooks/useBreakpoints';
import theme from 'lib/theme';
import { ThemeProvider } from 'emotion-theming';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';

jest.mock('components/SearchResultFetcher', () => () => <div data-testid="SearchResultFetcher" />);
jest.mock('components/CampaignMessaging', () => () => <div data-testid="CampaignMessaging" />);
jest.mock('./Messaging/DynamicMessageBox', () => () => <div data-testid="DynamicMessageBox" />);
jest.mock('./Sidebar/Filters', () => () => <div data-testid="Filters" />);
jest.mock('./Sidebar/MapButton', () => () => <div data-testid="MapButton" />);
jest.mock('./PayWith', () => () => <div data-testid="PayWith" />);
jest.mock('./SearchControls', () => () => <div data-testid="SearchControls" />);
jest.mock('./SearchError', () => () => <div data-testid="SearchError" />);
jest.mock('./SearchList', () => () => <div data-testid="SearchList" />);
jest.mock('./SearchResultSummary', () => ({ SearchResultSummaryConnected: () => <div data-testid="search-result-summary-desktop" /> }));
jest.mock('./SortSelect', () => () => <div data-testid="SortSelect" />);
jest.mock('./PromoArea', () => () => <div data-testid="PromoArea" />);
jest.mock('components/WelcomeMessage', () => () => <div data-testid="WelcomeMessage" />);
jest.mock('../RequestCallbackModal', () => ({ interactionType }) => <div data-testid="RequestCallbackModal">{interactionType}</div>);
jest.mock('./ListSearchMeta', () => () => <div data-testid="ListSearchMeta" />);

jest.mock('components/SkipToContentButton', () => () => <button data-testid="SkipToContentButton">Skip to Content</button>);
jest.mock('lib/qta/qta', () => ({ UpdateNavigationIconClose: () => <div data-testid="UpdateNavigationIconClose" /> }));

jest.mock('components/LegacyBrowserBoundary');
jest.mock('store/ui/uiSelectors');
jest.mock('hooks/useListSearchGa4Event');
jest.mock('hooks/useBreakpoints');
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

window.scroll = jest.fn();

const mockRouter = {
  query: {},
};

const mockRouterFromPropertyPage = {
  query: {
    backToSearch: true,
  },
};

const mockStore = configureStore([]);
const renderComponent = () => {
  const store = mockStore({});
  render(
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <ListSearchLayout />
      </ThemeProvider>
    </Provider>,
  );
};

beforeEach(() => {
  jest.clearAllMocks();
  LegacyBrowserBoundary.mockImplementation(({ children }) => children);
  mocked(getIsMobileApp).mockReturnValue(false);
  mocked(useRouter).mockReturnValue(mockRouter);
  mocked(useBreakpoints).mockReturnValue({ isLessThanBreakpoint: () => false });
  window.sessionStorage.clear();
});

describe('ListSearchLayout', () => {
  it('renders SearchResultFetcher', () => {
    renderComponent();
    expect(screen.getByTestId('SearchResultFetcher')).toBeInTheDocument();
  });

  it('renders the ListSearchMeta', () => {
    renderComponent();
    expect(screen.getByTestId('ListSearchMeta')).toBeInTheDocument();
  });

  it('renders page content', () => {
    renderComponent();
  });

  it('renders WelcomeMessage', () => {
    renderComponent();
    expect(screen.getByTestId('WelcomeMessage')).toBeInTheDocument();
  });

  it('renders SearchControls', () => {
    renderComponent();
    expect(screen.getByTestId('SearchControls')).toBeInTheDocument();
  });

  it('renders CampaignMessaging', () => {
    renderComponent();
    expect(screen.getByTestId('CampaignMessaging')).toBeInTheDocument();
  });

  it('renders PromoArea', () => {
    renderComponent();
    expect(screen.getByTestId('PromoArea')).toBeInTheDocument();
  });

  it('does NOT render PromoArea if viewing on mobile', () => {
    mocked(useBreakpoints).mockReturnValue({ isLessThanBreakpoint: () => true });
    renderComponent();
    expect(screen.queryByTestId('PromoArea')).not.toBeInTheDocument();
  });

  it('renders the skip to content button', () => {
    renderComponent();
    expect(screen.getByTestId('SkipToContentButton')).toBeInTheDocument();
  });

  it('renders the SearchResultSummary for desktop', () => {
    renderComponent();
    expect(screen.getByTestId('search-result-summary-desktop')).toBeInTheDocument();
  });

  it('renders the SortSelect', () => {
    renderComponent();
    expect(screen.getByTestId('SortSelect')).toBeInTheDocument();
  });

  describe('scroll back to the last height', () => {
    it('does NOT scroll on load when not a mobile device, has scrolledHeight, and coming from Property Page', () => {
      mocked(useRouter).mockReturnValue(mockRouterFromPropertyPage);
      window.sessionStorage.setItem('scrolledHeight', '100');
      renderComponent();
      expect(window.scroll).not.toHaveBeenCalled();
    });

    it('does NOT scroll on load when not coming from Property Page, is a mobile device, and has scrolledHeight saved', () => {
      mocked(useBreakpoints).mockReturnValue({ isLessThanBreakpoint: () => true });
      window.sessionStorage.setItem('scrolledHeight', '100');
      renderComponent();
      expect(window.scroll).not.toHaveBeenCalled();
    });

    it('does NOT scroll on load when scrolledHeight is not saved, coming from Property Page, and is a mobile device', () => {
      mocked(useBreakpoints).mockReturnValue({ isLessThanBreakpoint: () => true });
      renderComponent();
      expect(window.scroll).not.toHaveBeenCalled();
    });

    it('scrolls on load to the saved height when is a mobile device, has a scrolledHeight value and it is coming from Property Page', async () => {
      mocked(useRouter).mockReturnValue(mockRouterFromPropertyPage);
      mocked(useBreakpoints).mockReturnValue({ isLessThanBreakpoint: () => true });
      window.sessionStorage.setItem('scrolledHeight', '500');
      renderComponent();
      await waitFor(() => {
        expect(window.scroll).toHaveBeenCalledWith({ behavior: 'smooth', top: 500 });
      });
    });
  });

  it('renders the DynamicMessageBox', () => {
    renderComponent();
    expect(screen.getByTestId('DynamicMessageBox')).toBeInTheDocument();
  });

  it('renders the SidebarFilters', () => {
    renderComponent();
    expect(screen.getByTestId('Filters')).toBeInTheDocument();
  });

  it('renders the SearchError', () => {
    renderComponent();
    expect(screen.getByTestId('SearchError')).toBeInTheDocument();
  });

  it('renders SearchList', () => {
    renderComponent();
    expect(screen.getByTestId('SearchList')).toBeInTheDocument();
  });

  describe('maps button', () => {
    it('renders a map button', () => {
      renderComponent();
      expect(screen.getByTestId('MapButton')).toBeInTheDocument();
    });
  });
});

describe('RequestCallbackModal', () => {
  it('renders RequestCallbackModal with correct interaction type', () => {
    renderComponent();
    expect(screen.getByTestId('RequestCallbackModal')).toHaveTextContent('Multiroom booking');
  });
});

describe('isMobileApp', () => {
  it('does NOT render the UpdateNavigationIconClose when is false', () => {
    renderComponent();
    expect(screen.queryByTestId('UpdateNavigationIconClose')).not.toBeInTheDocument();
  });

  it('renders the UpdateNavigationIconClose when is true', () => {
    mocked(getIsMobileApp).mockReturnValue(true);
    renderComponent();
    expect(screen.getByTestId('UpdateNavigationIconClose')).toBeInTheDocument();
  });
});

describe('Additional ListSearchLayout Test Cases', () => {
  it('does not scroll if scrolledHeight is present but is 0', async () => {
    mocked(useRouter).mockReturnValue(mockRouterFromPropertyPage);
    mocked(useBreakpoints).mockReturnValue({ isLessThanBreakpoint: () => true });
    window.sessionStorage.setItem('scrolledHeight', '0');
    renderComponent();
    await waitFor(() => {
      expect(window.scroll).not.toHaveBeenCalled();
    });
  });
});
