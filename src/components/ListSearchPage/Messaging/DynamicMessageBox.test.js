import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { useDispatch, useSelector } from 'react-redux';
import DynamicMessageBox from './DynamicMessageBox';
import { getIsFlightBooker, getHasDismissedSearchMessaging } from 'store/user/userSelectors';
import { dismissSearchMessaging } from 'store/user/userActions';
import * as config from 'config';

jest.mock('config');
jest.mock('store/user/userSelectors');
jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
  useSelector: jest.fn(),
}));
jest.mock('store/user/userActions', () => ({
  dismissSearchMessaging: jest.fn(),
}));
const dispatchMock = jest.fn();

describe('<DynamicMessageBox />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useDispatch.mockReturnValue(dispatchMock);
    config.FLIGHT_BOOKER_MESSAGE_ENABLED = true;
    getIsFlightBooker.mockReturnValue(false);
    getHasDismissedSearchMessaging.mockReturnValue(true);
    useSelector.mockImplementation((selector) => {
      if (selector === getIsFlightBooker) {
        return getIsFlightBooker();
      }
      if (selector === getHasDismissedSearchMessaging) {
        return getHasDismissedSearchMessaging();
      }
      return undefined;
    });
  });

  describe('when the banner has been dismissed before', () => {
    it('does not render the banner', () => {
      render(<DynamicMessageBox />);
      expect(screen.queryByRole('region', { name: /flight booker message/i })).not.toBeInTheDocument();
    });
  });

  describe("when the banner hasn't been dismissed before", () => {
    beforeEach(() => {
      getHasDismissedSearchMessaging.mockReturnValue(false);
    });

    describe('when the user is not a flightBooker', () => {
      it('does not render anything', () => {
        getIsFlightBooker.mockReturnValue(false);
        const { container } = render(<DynamicMessageBox />);
        expect(container).toBeEmptyDOMElement();
      });
    });

    describe('when the user is a flightBooker', () => {
      beforeEach(() => {
        getIsFlightBooker.mockReturnValue(true);
      });

      it('renders the correct message', () => {
        render(<DynamicMessageBox />);
        expect(screen.getByTestId('flight-booker-message-box')).toBeInTheDocument();
      });

      describe('when FLIGHT_BOOKER_MESSAGE_ENABLED is off', () => {
        beforeEach(() => {
          config.FLIGHT_BOOKER_MESSAGE_ENABLED = false;
        });

        it('does not render a message', () => {
          render(<DynamicMessageBox />);

          expect(screen.queryByTestId('flight-booker-message-box')).not.toBeInTheDocument();
        });
      });

      describe('when clicking the close icon', () => {
        it('dispatches the dismissSearchMessaging action', async () => {
          dismissSearchMessaging.mockReturnValue({ type: 'DISMISS_SEARCH_MESSAGING' });
          render(<DynamicMessageBox />);
          const closeButton = screen.getByTitle('close');
          await userEvent.click(closeButton);

          expect(dispatchMock).toHaveBeenCalledWith(dismissSearchMessaging());
        });
      });
    });
  });
});
