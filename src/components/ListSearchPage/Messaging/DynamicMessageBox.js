import React, { useCallback } from 'react';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { useDispatch, useSelector } from 'react-redux';
import { Flex, Box, Container, Icon, Text } from '@qga/roo-ui/components';
import { pebbleRedNarrow } from '@qga/roo-ui/assets';
import { getIsFlightBooker, getHasDismissedSearchMessaging } from 'store/user/userSelectors';
import { mediaQuery } from 'lib/styledSystem';
import { dismissSearchMessaging } from 'store/user/userActions';
import { FLIGHT_BOOKER_MESSAGE_ENABLED } from 'config';

const CloseIcon = styled(Icon)`
  cursor: pointer;
  width: ${themeGet('space.5')};
  height: ${themeGet('space.5')};
  ${mediaQuery.minWidth.sm} {
    background: transparent;
    border-radius: ${themeGet('radii.rounded')};
    width: ${themeGet('space.8')};
    height: ${themeGet('space.8')};
    padding: ${themeGet('space.1')};
    &:hover {
      background: rgba(34, 34, 34, 0.3);
    }
  }
`;
CloseIcon.displayName = 'CloseIcon';

const Background = styled(Box)`
  color: ${themeGet('colors.white')};
  background: ${themeGet('colors.brand.primary')};
  border-radius: ${themeGet('radii.default')};
  margin: ${themeGet('space.6')} auto;
  margin-top: ${themeGet('space.6')};
`;

const PebbleBackground = styled(Container)`
  border-radius: 3px;
  background: url(${pebbleRedNarrow}) no-repeat;
  background-position: -320% 95%;
  ${mediaQuery.minWidth.sm} {
    background-position: 133% 95%;
  }
`;

const WhiteBaseText = styled(Text)`
  color: ${themeGet('colors.white')};
  font-size: ${themeGet('fontSizes.base')};
`;

const FlightBookerMessageBox = () => {
  const dispatch = useDispatch();
  const onDismiss = useCallback(() => {
    dispatch(dismissSearchMessaging());
  }, [dispatch]);

  return (
    <Background mb={[3, 8, 8]} data-testid="flight-booker-message-box">
      <PebbleBackground>
        <Flex py={['2', '4']}>
          <Box>
            <WhiteBaseText display={['none', 'block']} fontWeight="bold" mb={0}>
              You now have access to Qantas Flyer Deals!
            </WhiteBaseText>
            <WhiteBaseText mr={['1', 'auto']}>When you book a flight with us you can access exclusive hotel deals.</WhiteBaseText>
          </Box>
          <Box ml={['1', 'auto']}>
            <CloseIcon name="close" onClick={onDismiss} data-testid="close-flightbooker-banner" />
          </Box>
        </Flex>
      </PebbleBackground>
    </Background>
  );
};

const DynamicMessageBox = () => {
  const isFlightBooker = useSelector(getIsFlightBooker);
  const hasDismissedSearchMessaging = useSelector(getHasDismissedSearchMessaging);

  if (!FLIGHT_BOOKER_MESSAGE_ENABLED || hasDismissedSearchMessaging || !isFlightBooker) return null;

  return <FlightBookerMessageBox />;
};

export default DynamicMessageBox;
