import React from 'react';
import { render } from '@testing-library/react';
import { getPageTitle } from 'store/search/searchSelectors';
import { HOTELS_BRAND_NAME } from 'config';
import ListSearchMeta from './ListSearchMeta';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';

jest.mock('store/search/searchSelectors');

const mockStore = configureStore([]);
const renderComponent = () => {
  const store = mockStore({});
  render(
    <Provider store={store}>
      <ListSearchMeta />
    </Provider>,
  );
};

describe('ListSearchMeta', () => {
  it('sets the title of the webpage', () => {
    getPageTitle.mockReturnValue('my geocities website');
    renderComponent();

    expect(document.title).toEqual('my geocities website');
  });

  it('sets the search meta', () => {
    getPageTitle.mockReturnValue('my geocities website');
    renderComponent();

    expect(document.querySelector('meta[name="hotels-booking-stage"]')).toHaveAttribute('content', 'search');
    expect(document.querySelector('meta[name="robots"]')).toHaveAttribute('content', 'noindex, follow');
    expect(document.querySelector('meta[name="description"]')).toHaveAttribute(
      'content',
      `Search results | ${HOTELS_BRAND_NAME} Australia`,
    );
  });
});
