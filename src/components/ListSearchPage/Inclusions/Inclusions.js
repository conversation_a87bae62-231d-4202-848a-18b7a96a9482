import React from 'react';
import PropTypes from 'prop-types';
import compact from 'lodash/compact';
import take from 'lodash/take';
import { Flex, Box, Icon, Text } from '@qga/roo-ui/components';
import useGlobalInclusions from 'hooks/optimizely/useGlobalInclusions';

const ORDERED_INCLUSION_WHITELIST = [
  { code: 'internet', icon: 'wifi' },
  { code: 'breakfast', icon: 'restaurant' },
  { code: 'parking', icon: 'directionsCar' },
];

const Inclusions = ({ inclusions: allInclusions, ...rest }) => {
  const { isGlobalInclusions } = useGlobalInclusions();

  const displayInclusions = isGlobalInclusions
    ? allInclusions
    : compact(
        ORDERED_INCLUSION_WHITELIST.map((inclusionWhitelist) => {
          const inclusion = allInclusions.find((inclusion) => inclusion.code === inclusionWhitelist.code);
          return inclusion ? { ...inclusion, icon: inclusionWhitelist.icon } : null;
        }),
      );

  const maxDisplayInclusions = take(displayInclusions, 3);

  return (
    <Flex data-testid="search-inclusions" flexDirection="column" alignItems="flex-start" {...rest}>
      {maxDisplayInclusions.map((inclusion, index) => (
        <Box pb={2} key={index} data-testid="inclusion" title={inclusion.description}>
          <Flex justifyContent="start" alignItems="center" borderRadius="rounded">
            <Icon name={inclusion.icon} color="ui.iconBadge" size={20} mr={2} role="img" />
            <Text data-testid="inclusion-name">{inclusion.name}</Text>
          </Flex>
        </Box>
      ))}
      {displayInclusions.length > 3 && (
        <Text data-testid="more-inclusions">
          + {displayInclusions.length - 3} more {displayInclusions.length - 3 === 1 ? 'inclusion' : 'inclusions'}
        </Text>
      )}
    </Flex>
  );
};

Inclusions.propTypes = {
  inclusions: PropTypes.array.isRequired,
};

export default Inclusions;
