import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import Inclusions from './Inclusions';
import {
  defaultInclusions,
  twoInclusions,
  threeInclusions,
  fourInclusions,
  whiteListOrderIconNames,
  whiteListOrderTitles,
  receivedOrderIconNames,
  receivedOrderTitles,
} from '../fixtures';
import useGlobalInclusions from 'hooks/optimizely/useGlobalInclusions';

jest.mock('hooks/optimizely/useGlobalInclusions');

const renderComponent = (inclusions = defaultInclusions) => render(<Inclusions inclusions={inclusions} />);

describe('The Inclusions component', () => {
  describe('when useGlobalInclusions is true', () => {
    const useGlobalInclusionsValue = true;

    beforeEach(() => {
      jest.clearAllMocks();
      useGlobalInclusions.mockReturnValue({
        isReady: true,
        isGlobalInclusions: useGlobalInclusionsValue,
      });
    });

    describe('when there are more than 3 inclusions', () => {
      it('renders a maximum of 3 icons', () => {
        renderComponent(defaultInclusions);
        const icons = screen.getAllByRole('img');
        expect(icons).toHaveLength(3);
      });

      describe('The Title', () => {
        it('is added from the description', () => {
          renderComponent(defaultInclusions);
          const expectedTitles = useGlobalInclusionsValue ? receivedOrderTitles : whiteListOrderTitles;
          const inclusionElements = screen.getAllByTestId('inclusion');

          inclusionElements.forEach((element, index) => {
            expect(element).toHaveAttribute('title', expectedTitles[index]);
          });
        });
      });

      describe('The Icons', () => {
        it('renders the icons in the correct order', () => {
          renderComponent(defaultInclusions);
          const expectedIconNames = useGlobalInclusionsValue ? receivedOrderIconNames : whiteListOrderIconNames;
          const icons = screen.getAllByRole('img');
          icons.forEach((icon, index) => {
            expect(icon).toHaveAttribute('title', expectedIconNames[index]);
          });
        });
      });

      describe('The "+ N more inclusions" text', () => {
        it('is displayed when there are more than 3 inclusions', () => {
          renderComponent(defaultInclusions);
          expect(screen.getByText('+ 2 more inclusions')).toBeInTheDocument();
        });

        it('displays correct inclusion spelling if there is only 1 more inclusion', () => {
          renderComponent(fourInclusions);
          expect(screen.getByText('+ 1 more inclusion')).toBeInTheDocument();
        });

        it('is not displayed when there are 3 or fewer inclusions', () => {
          renderComponent(threeInclusions);
          expect(screen.queryByText(/more inclusion/i)).not.toBeInTheDocument();

          renderComponent(twoInclusions);
          expect(screen.queryByText(/more inclusion/i)).not.toBeInTheDocument();
        });
      });
    });
  });

  describe('when useGlobalInclusions is false', () => {
    const useGlobalInclusionsValue = false;

    beforeEach(() => {
      jest.clearAllMocks();
      useGlobalInclusions.mockReturnValue({
        isReady: true,
        isGlobalInclusions: useGlobalInclusionsValue,
      });
    });

    describe('when there are more than 3 inclusions', () => {
      it('renders a maximum of 3 icons', () => {
        renderComponent(defaultInclusions);
        const icons = screen.getAllByRole('img');
        expect(icons).toHaveLength(3);
      });

      describe('The Title', () => {
        it('is added from the description', () => {
          renderComponent(defaultInclusions);
          const expectedTitles = useGlobalInclusionsValue ? receivedOrderTitles : whiteListOrderTitles;
          const inclusionElements = screen.getAllByTestId('inclusion');

          inclusionElements.forEach((element, index) => {
            expect(element).toHaveAttribute('title', expectedTitles[index]);
          });
        });
      });

      describe('The Icons', () => {
        it('renders the icons in the correct order', () => {
          renderComponent(defaultInclusions);
          const expectedIconNames = useGlobalInclusionsValue ? receivedOrderIconNames : whiteListOrderIconNames;
          const icons = screen.getAllByRole('img');

          icons.forEach((icon, index) => {
            expect(icon).toHaveAttribute('title', expectedIconNames[index]);
          });
        });
      });

      describe('The "+ N more inclusions" text', () => {
        it('is displayed when there are more than 3 inclusions', () => {
          renderComponent(defaultInclusions);
          expect(screen.queryByText('+ 2 more inclusions')).not.toBeInTheDocument();
        });

        it('displays correct inclusion spelling if there is only 1 more inclusion', () => {
          renderComponent(fourInclusions);
          expect(screen.queryByText('+ 1 more inclusion')).not.toBeInTheDocument();
        });

        it('is not displayed when there are 3 or fewer inclusions', () => {
          renderComponent(threeInclusions);
          expect(screen.queryByText(/more inclusion/i)).not.toBeInTheDocument();

          renderComponent(twoInclusions);
          expect(screen.queryByText(/more inclusion/i)).not.toBeInTheDocument();
        });
      });
    });
  });
});
