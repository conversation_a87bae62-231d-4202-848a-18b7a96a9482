import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';
import ListSearchPage from './ListSearchPage';
import { clearResults } from 'store/search/searchActions';

jest.mock('@loadable/component', () => {
  return (importFn) => {
    if (!importFn.chunkName) {
      importFn.chunkName = () => 'ListSearchLayout';
    }
    return (props) => <div data-testid="list-search-layout" {...props} />;
  };
});

const mockStore = configureStore();

const renderComponent = (props) => {
  const store = mockStore({});

  render(
    <Provider store={store}>
      <ListSearchPage {...props} />
    </Provider>,
  );
};

describe('ListSearchPage', () => {
  it('renders ListSearchLayout and passes all relevant props to it', () => {
    renderComponent({ a: 1, b: 2, c: 3 });

    const listSearchLayout = screen.getByTestId('list-search-layout');

    expect(listSearchLayout).toBeInTheDocument();
    expect(listSearchLayout).toHaveAttribute('a', '1');
    expect(listSearchLayout).toHaveAttribute('b', '2');
    expect(listSearchLayout).toHaveAttribute('c', '3');
  });

  it('dispatches clearResults action when onExitPage is called', () => {
    const store = mockStore();
    ListSearchPage.onExitPage({ store });

    expect(store.getActions()).toEqual([clearResults()]);
  });
});
