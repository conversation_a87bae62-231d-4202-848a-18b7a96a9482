import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import QFFInformation from './QFFInformation';
import { configureStore } from 'redux-mock-store';

import { useDataLayer } from 'hooks/useDataLayer';
import { Provider } from 'react-redux';
import { useModal } from 'lib/hooks';
jest.mock('hooks/useDataLayer');

jest.mock('lib/hooks', () => ({
  useModal: jest.fn(),
}));

jest.mock('@qga/roo-ui/components', () => ({
  Text: (props) => <span data-testid="roo-ui-text" {...props}></span>,
  Flex: ({ children, 'data-testid': dataTestId, ...props }) => (
    <div data-testid={dataTestId} {...props}>
      {children}
    </div>
  ),
  Box: (props) => <div data-testid="roo-ui-box" {...props}></div>,
  Icon: (props) => <svg data-testid="roo-ui-icon" {...props}></svg>,
}));

jest.mock('components/Modal', () => {
  return ({ children, isOpen, onClose, ...props }) =>
    isOpen ? (
      <div data-testid="mock-modal" {...props}>
        {children}
        <button data-testid="mock-close-button" onClick={onClose}>
          Close
        </button>
      </div>
    ) : null;
});

const emitInteractionEvent = jest.fn();

const mockStore = configureStore([]);
const renderComponent = () => {
  const store = mockStore({});
  return render(
    <Provider store={store}>
      <QFFInformation />
    </Provider>,
  );
};

describe('<QFFInformation />', () => {
  beforeEach(() => {
    useDataLayer.mockReturnValue({ emitInteractionEvent });
    jest.clearAllMocks();
    useModal.mockImplementation(() => ({
      openModal: jest.fn(),
      modalProps: {
        isOpen: false,
        onClose: jest.fn(),
      },
    }));
  });

  it('renders the Modal with text and title when the QFF link is clicked', async () => {
    const mockOpenModal = jest.fn();
    const mockCloseModal = jest.fn();

    useModal.mockImplementationOnce(() => ({
      openModal: mockOpenModal,
      modalProps: {
        isOpen: false,
        onClose: mockCloseModal,
      },
    }));

    const { rerender } = renderComponent();

    const qffLink = screen.getByRole('button', { name: /when will i get my points\?/i });
    await userEvent.click(qffLink);

    expect(mockOpenModal).toHaveBeenCalledTimes(1);

    useModal.mockImplementationOnce(() => ({
      openModal: mockOpenModal,
      modalProps: { isOpen: true, onClose: mockCloseModal },
    }));

    rerender(<QFFInformation />);

    expect(screen.getByTestId('mock-modal')).toBeInTheDocument();
    expect(screen.getByTestId('mock-modal')).toHaveAttribute('title', 'Earn Qantas Points');
    expect(
      screen.getByText(
        /Qantas Points will automatically be credited within 8 weeks of checkout if the name and number match the details in the Frequent Flyer account\./i,
      ),
    ).toBeInTheDocument();
  });

  it('dispatches an event to the dataLayer', async () => {
    renderComponent();

    const qffLink = screen.getByRole('button', { name: /when will i get my points\?/i });
    await userEvent.click(qffLink);
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'QFF Earn Points',
      value: 'Points FAQ Link Selected',
    });
  });

  it('closes the modal when the close button is clicked', async () => {
    const mockOpenModal = jest.fn();
    const mockCloseModal = jest.fn();

    useModal.mockImplementationOnce(() => ({
      openModal: mockOpenModal,
      modalProps: { isOpen: true, onClose: mockCloseModal },
    }));

    const { rerender } = renderComponent();

    expect(screen.queryByTestId('mock-modal')).toBeInTheDocument();
    await userEvent.click(screen.getByRole('button', { name: /close/i }));
    expect(mockCloseModal).toHaveBeenCalledTimes(1);
    useModal.mockImplementationOnce(() => ({
      openModal: mockOpenModal,
      modalProps: { isOpen: false, onClose: mockCloseModal },
    }));

    rerender(<QFFInformation />);

    expect(screen.queryByTestId('mock-modal')).not.toBeInTheDocument();
  });

  it('closes the modal when the escape key is pressed', async () => {
    const mockOpenModal = jest.fn();
    const mockCloseModal = jest.fn();
    useModal.mockImplementationOnce(() => ({
      openModal: mockOpenModal,
      modalProps: { isOpen: true, onClose: mockCloseModal },
    }));

    const { rerender } = renderComponent();

    const qffLink = screen.getByRole('button', { name: /when will i get my points\?/i });
    await userEvent.click(qffLink);

    expect(screen.queryByTestId('mock-modal')).toBeInTheDocument();

    await userEvent.keyboard('{escape}');

    useModal.mockImplementationOnce(() => ({
      modalProps: { isOpen: false, onClose: jest.fn() },
    }));

    rerender(<QFFInformation />);

    expect(screen.queryByTestId('mock-modal')).not.toBeInTheDocument();
  });
});
